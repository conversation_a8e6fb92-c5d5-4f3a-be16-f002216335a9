#!/usr/bin/env python3
"""
测试增强的PDF解析功能
"""
import asyncio
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from app.services.document_processor import DocumentProcessor
from app.models.schemas import FileType
from app.core.config import get_settings

async def test_enhanced_pdf_parsing():
    """测试增强的PDF解析功能"""
    print("🚀 开始测试增强的PDF解析功能...")
    
    # 初始化文档处理器
    processor = DocumentProcessor()
    
    # 查找测试PDF文件
    upload_dir = Path(processor.settings.upload_path)
    pdf_files = list(upload_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 未找到PDF文件进行测试")
        print(f"请将PDF文件放置在: {upload_dir}")
        return False
    
    print(f"📁 找到 {len(pdf_files)} 个PDF文件")
    
    # 测试每个PDF文件
    for pdf_file in pdf_files[:3]:  # 限制测试前3个文件
        print(f"\n{'='*60}")
        print(f"📄 测试文件: {pdf_file.name}")
        print(f"📏 文件大小: {pdf_file.stat().st_size / 1024:.1f} KB")
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 提取文本
            extracted_text = await processor.extract_text_from_file(
                pdf_file.stem, FileType.PDF
            )
            
            # 记录结束时间
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 分析结果
            if extracted_text:
                text_length = len(extracted_text)
                lines_count = len(extracted_text.split('\n'))
                words_count = len(extracted_text.split())
                
                print(f"✅ 解析成功!")
                print(f"⏱️  处理时间: {processing_time:.2f} 秒")
                print(f"📊 文本统计:")
                print(f"   - 字符数: {text_length:,}")
                print(f"   - 行数: {lines_count:,}")
                print(f"   - 词数: {words_count:,}")
                
                # 质量评估
                quality_score = processor._assess_text_quality_enhanced(extracted_text)
                print(f"   - 质量评分: {quality_score:.3f}")
                
                # 显示内容预览
                preview_length = min(500, len(extracted_text))
                preview = extracted_text[:preview_length]
                print(f"\n📝 内容预览 (前{preview_length}字符):")
                print("-" * 50)
                print(preview)
                if len(extracted_text) > preview_length:
                    print("...")
                print("-" * 50)
                
                # 检查文本质量
                await analyze_text_quality(extracted_text, processor)
                
            else:
                print("❌ 未能提取到任何文本内容")
                
        except Exception as e:
            print(f"❌ 解析失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print("🎉 测试完成!")
    return True

async def analyze_text_quality(text: str, processor: DocumentProcessor):
    """分析文本质量"""
    print(f"\n🔍 文本质量分析:")
    
    # 字符类型统计
    import re
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    english_chars = len(re.findall(r'[a-zA-Z]', text))
    digits = len(re.findall(r'\d', text))
    spaces = len(re.findall(r'\s', text))
    punctuation = len(re.findall(r'[.,;:!?()[\]{}"\'-]', text))
    
    total_chars = len(text)
    
    print(f"   - 中文字符: {chinese_chars} ({chinese_chars/total_chars*100:.1f}%)")
    print(f"   - 英文字符: {english_chars} ({english_chars/total_chars*100:.1f}%)")
    print(f"   - 数字字符: {digits} ({digits/total_chars*100:.1f}%)")
    print(f"   - 空白字符: {spaces} ({spaces/total_chars*100:.1f}%)")
    print(f"   - 标点符号: {punctuation} ({punctuation/total_chars*100:.1f}%)")
    
    # 检查特殊字符
    special_chars = total_chars - (chinese_chars + english_chars + digits + spaces + punctuation)
    special_ratio = special_chars / total_chars if total_chars > 0 else 0
    
    print(f"   - 特殊字符: {special_chars} ({special_ratio*100:.1f}%)")
    
    # 质量判断
    if special_ratio < 0.1:
        print("   ✅ 文本质量优秀")
    elif special_ratio < 0.3:
        print("   ⚠️ 文本质量良好")
    else:
        print("   ❌ 文本质量较差，可能包含乱码")
    
    # 检查结构
    lines = text.split('\n')
    non_empty_lines = [line.strip() for line in lines if line.strip()]
    
    print(f"   - 总行数: {len(lines)}")
    print(f"   - 非空行数: {len(non_empty_lines)}")
    
    # 检查是否包含表格
    if '|' in text and text.count('|') > 10:
        print("   📊 检测到表格内容")
    
    # 检查是否包含页码标识
    if re.search(r'第\s*\d+\s*页', text):
        print("   📄 检测到页码标识")

def test_pdf_structure_analysis():
    """测试PDF结构分析功能"""
    print("\n🔬 测试PDF结构分析功能...")

    processor = DocumentProcessor()

    upload_dir = Path(processor.settings.upload_path)
    pdf_files = list(upload_dir.glob("*.pdf"))
    
    for pdf_file in pdf_files[:2]:  # 测试前2个文件
        print(f"\n📄 分析文件: {pdf_file.name}")
        
        try:
            pdf_info = processor._analyze_pdf_structure(str(pdf_file))
            
            print(f"📊 PDF结构信息:")
            print(f"   - 页数: {pdf_info.get('page_count', 0)}")
            print(f"   - 包含文本: {'是' if pdf_info.get('has_text') else '否'}")
            print(f"   - 包含图片: {'是' if pdf_info.get('has_images') else '否'}")
            print(f"   - 包含表格: {'是' if pdf_info.get('has_tables') else '否'}")
            print(f"   - 文本密度: {pdf_info.get('text_density', 0):.2f}")
            print(f"   - 平均字符/页: {pdf_info.get('avg_chars_per_page', 0):.0f}")
            print(f"   - 是否扫描版: {'是' if pdf_info.get('is_scanned') else '否'}")
            print(f"   - 编码问题: {'是' if pdf_info.get('encoding_issues') else '否'}")
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    print("🧪 增强PDF解析功能测试")
    print("=" * 60)
    
    # 测试PDF结构分析
    test_pdf_structure_analysis()
    
    # 测试增强的PDF解析
    asyncio.run(test_enhanced_pdf_parsing())
