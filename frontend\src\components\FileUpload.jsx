/**
 * 文件上传组件
 */
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, message, Card, Progress, List, Button } from 'antd';
import { InboxOutlined, FileTextOutlined, DeleteOutlined } from '@ant-design/icons';
import { uploadDocument } from '../services/api';

const { Dragger } = Upload;

const FileUpload = ({ onUploadSuccess, onUploadError }) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [isMobile, setIsMobile] = useState(false);

  // 检测屏幕尺寸
  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 576); // 只有真正的移动设备才算移动端
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // 支持的文件类型
  const supportedTypes = {
    'text/plain': ['.txt'],
    'text/markdown': ['.md', '.markdown'],
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'application/msword': ['.doc'],
  };

  // 文件类型图标映射
  const getFileIcon = (fileName) => {
    const ext = fileName.toLowerCase().split('.').pop();
    const iconMap = {
      txt: '📄',
      md: '📝',
      markdown: '📝',
      pdf: '📕',
      docx: '📘',
      doc: '📘',
    };
    return iconMap[ext] || '📄';
  };

  // 处理文件上传
  const handleUpload = async (file) => {
    setUploading(true);
    setUploadProgress(0);

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const result = await uploadDocument(file);
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (result.success) {
        message.success(`文件 "${file.name}" 上传成功！`);
        
        // 添加到已上传文件列表
        setUploadedFiles(prev => [...prev, {
          id: result.document_id,
          name: file.name,
          size: file.size,
          status: 'processing',
          ...result.document_info
        }]);

        // 通知父组件
        if (onUploadSuccess) {
          onUploadSuccess(result);
        }
      } else {
        throw new Error(result.message || '上传失败');
      }
    } catch (error) {
      message.error(`上传失败: ${error.message}`);
      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setUploading(false);
      setTimeout(() => setUploadProgress(0), 1000);
    }
  };

  // 自定义上传逻辑
  const customRequest = ({ file, onSuccess, onError }) => {
    handleUpload(file)
      .then(() => onSuccess())
      .catch(onError);
  };

  // 文件验证
  const beforeUpload = (file) => {
    const isValidType = Object.keys(supportedTypes).some(type => 
      file.type === type || supportedTypes[type].some(ext => 
        file.name.toLowerCase().endsWith(ext)
      )
    );

    if (!isValidType) {
      message.error('不支持的文件类型！支持 TXT、Markdown、PDF、Word 文档');
      return false;
    }

    const isLt50M = file.size / 1024 / 1024 < 50;
    if (!isLt50M) {
      message.error('文件大小不能超过 50MB！');
      return false;
    }

    return true;
  };

  // 移除文件
  const removeFile = (fileId) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  return (
    <div style={{
      padding: isMobile ? '12px' : '24px',
      maxWidth: '1000px',
      margin: '0 auto',
      width: '100%'
    }}>
      <Card title="📁 文档上传" style={{ marginBottom: '20px' }}>
        <Dragger
          name="file"
          multiple={false}
          customRequest={customRequest}
          beforeUpload={beforeUpload}
          showUploadList={false}
          disabled={uploading}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
          </p>
          <p className="ant-upload-text">
            点击或拖拽文件到此区域上传
          </p>
          <p className="ant-upload-hint">
            支持 TXT、Markdown、PDF、Word 文档格式，单个文件不超过 50MB
          </p>
        </Dragger>

        {uploading && (
          <div style={{ marginTop: '16px' }}>
            <Progress 
              percent={uploadProgress} 
              status={uploadProgress === 100 ? 'success' : 'active'}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
          </div>
        )}
      </Card>

      {uploadedFiles.length > 0 && (
        <Card title="📋 最近上传" size="small">
          <List
            size="small"
            dataSource={uploadedFiles}
            renderItem={(file) => (
              <List.Item
                actions={[
                  <Button
                    type="text"
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => removeFile(file.id)}
                  />
                ]}
              >
                <List.Item.Meta
                  avatar={<span style={{ fontSize: '20px' }}>{getFileIcon(file.name)}</span>}
                  title={file.name}
                  description={
                    <div>
                      <span>大小: {(file.size / 1024).toFixed(1)} KB</span>
                      <span style={{ marginLeft: '16px' }}>
                        状态: {file.status === 'processing' ? '🔄 处理中' : '✅ 已完成'}
                      </span>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      )}
    </div>
  );
};

export default FileUpload;
