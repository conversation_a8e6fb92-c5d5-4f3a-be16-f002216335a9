/**
 * 文档管理组件
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Tag,
  Space,
  Popconfirm,
  message,
  Modal,
  Typography,
  Row,
  Col,
  Statistic,
  Tooltip
} from 'antd';
import {
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  SyncOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { getDocuments, deleteDocument, getDocument } from '../services/api';

const { Text, Paragraph } = Typography;

const DocumentManager = () => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    indexed: 0,
    processing: 0,
    error: 0
  });

  // 检测屏幕尺寸
  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 576); // 只有真正的移动设备才算移动端
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // 加载文档列表
  const loadDocuments = async () => {
    setLoading(true);
    try {
      const response = await getDocuments();
      if (response.success) {
        setDocuments(response.documents);
        calculateStats(response.documents);
      } else {
        message.error('加载文档列表失败');
      }
    } catch (error) {
      message.error(`加载失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 计算统计信息
  const calculateStats = (docs) => {
    const stats = {
      total: docs.length,
      indexed: docs.filter(d => d.status === 'indexed').length,
      processing: docs.filter(d => d.status === 'processing').length,
      error: docs.filter(d => d.status === 'error').length
    };
    setStats(stats);
  };

  // 删除文档
  const handleDelete = async (documentId) => {
    try {
      const response = await deleteDocument(documentId);
      if (response.success) {
        message.success('文档删除成功');
        loadDocuments(); // 重新加载列表
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error(`删除失败: ${error.message}`);
    }
  };

  // 预览文档
  const handlePreview = async (documentId) => {
    try {
      const document = await getDocument(documentId);
      setSelectedDocument(document);
      setPreviewVisible(true);
    } catch (error) {
      message.error(`获取文档详情失败: ${error.message}`);
    }
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusMap = {
      uploading: { color: 'blue', icon: <SyncOutlined spin />, text: '上传中' },
      processing: { color: 'orange', icon: <SyncOutlined spin />, text: '处理中' },
      indexed: { color: 'green', icon: <CheckCircleOutlined />, text: '已索引' },
      error: { color: 'red', icon: <ExclamationCircleOutlined />, text: '错误' }
    };
    
    const config = statusMap[status] || statusMap.error;
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 获取文件类型图标
  const getFileTypeIcon = (fileType) => {
    const iconMap = {
      txt: '📄',
      md: '📝',
      markdown: '📝',
      pdf: '📕',
      docx: '📘',
      doc: '📘'
    };
    return iconMap[fileType] || '📄';
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 格式化时间
  const formatTime = (timeString) => {
    return new Date(timeString).toLocaleString('zh-CN');
  };

  // 表格列定义
  const columns = [
    {
      title: '文档名称',
      dataIndex: 'filename',
      key: 'filename',
      render: (text, record) => (
        <Space>
          <span style={{ fontSize: '16px' }}>
            {getFileTypeIcon(record.file_type)}
          </span>
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'file_type',
      key: 'file_type',
      render: (type) => <Tag>{type.toUpperCase()}</Tag>,
    },
    {
      title: '大小',
      dataIndex: 'file_size',
      key: 'file_size',
      render: (size) => formatFileSize(size),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
    },
    {
      title: '上传时间',
      dataIndex: 'upload_time',
      key: 'upload_time',
      render: (time) => formatTime(time),
    },
    {
      title: '分块数',
      dataIndex: 'chunk_count',
      key: 'chunk_count',
      render: (count) => count ? <Tag color="blue">{count}</Tag> : '-',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="预览">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handlePreview(record.id)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个文档吗？"
            description="删除后将无法恢复，且会从索引中移除。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    loadDocuments();
  }, []);

  return (
    <div style={{
      padding: isMobile ? '12px' : '24px',
      maxWidth: '1400px',
      margin: '0 auto',
      width: '100%'
    }}>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '20px' }}>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card>
            <Statistic
              title="总文档数"
              value={stats.total}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card>
            <Statistic
              title="已索引"
              value={stats.indexed}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card>
            <Statistic
              title="处理中"
              value={stats.processing}
              prefix={<SyncOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6} lg={6}>
          <Card>
            <Statistic
              title="错误"
              value={stats.error}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 文档列表 */}
      <Card
        title="📚 文档管理"
        extra={
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={loadDocuments}
            loading={loading}
          >
            刷新
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={documents}
          rowKey="id"
          loading={loading}
          scroll={{ x: 800 }}
          pagination={{
            pageSize: isMobile ? 5 : 10,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            showTotal: (total) => `共 ${total} 个文档`,
            simple: isMobile,
          }}
        />
      </Card>

      {/* 文档预览模态框 */}
      <Modal
        title={selectedDocument ? `📄 ${selectedDocument.filename}` : '文档预览'}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={isMobile ? '95%' : 800}
        style={isMobile ? { top: 20 } : {}}
      >
        {selectedDocument && (
          <div>
            <Row gutter={[16, 8]} style={{ marginBottom: '16px' }}>
              <Col xs={24} sm={8}>
                <Text strong>文件类型:</Text> {selectedDocument.file_type.toUpperCase()}
              </Col>
              <Col xs={24} sm={8}>
                <Text strong>文件大小:</Text> {formatFileSize(selectedDocument.file_size)}
              </Col>
              <Col xs={24} sm={8}>
                <Text strong>状态:</Text> {getStatusTag(selectedDocument.status)}
              </Col>
            </Row>

            <Row gutter={[16, 8]} style={{ marginBottom: '16px' }}>
              <Col xs={24} sm={12}>
                <Text strong>上传时间:</Text> {formatTime(selectedDocument.upload_time)}
              </Col>
              <Col xs={24} sm={12}>
                <Text strong>分块数量:</Text> {selectedDocument.chunk_count || '未知'}
              </Col>
            </Row>

            {selectedDocument.content_preview && (
              <div>
                <Text strong>内容预览:</Text>
                <Paragraph
                  style={{
                    marginTop: '8px',
                    padding: '12px',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '4px',
                    maxHeight: '300px',
                    overflow: 'auto'
                  }}
                >
                  {selectedDocument.content_preview}
                </Paragraph>
              </div>
            )}

            {selectedDocument.error_message && (
              <div style={{ marginTop: '16px' }}>
                <Text strong type="danger">错误信息:</Text>
                <Paragraph type="danger" style={{ marginTop: '8px' }}>
                  {selectedDocument.error_message}
                </Paragraph>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default DocumentManager;
