/**
 * 搜索界面组件
 */
import React, { useState } from 'react';
import {
  Input,
  Button,
  Card,
  Tag,
  Spin,
  Empty,
  message,
  Slider,
  Row,
  Col,
  Typography,
  Space
} from 'antd';
import { SearchOutlined, ClockCircleOutlined, FilterOutlined } from '@ant-design/icons';
import { searchDocuments } from '../services/api';
import SearchResultCard from './SearchResultCard';

const { Search } = Input;
const { Text, Paragraph, Title } = Typography;

const SearchInterface = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTime, setSearchTime] = useState(0);
  const [topK, setTopK] = useState(5);
  const [hasSearched, setHasSearched] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测屏幕尺寸
  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 576); // 只有真正的移动设备才算移动端
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // 执行搜索
  const handleSearch = async (searchQuery) => {
    if (!searchQuery.trim()) {
      message.warning('请输入搜索内容');
      return;
    }

    setLoading(true);
    setHasSearched(true);

    try {
      const response = await searchDocuments(searchQuery, topK);
      
      if (response.success) {
        setResults(response.results);
        setSearchTime(response.search_time);
        message.success(`找到 ${response.total_results} 个相关结果`);
      } else {
        message.error('搜索失败，请重试');
        setResults([]);
      }
    } catch (error) {
      message.error(`搜索出错: ${error.message}`);
      setResults([]);
    } finally {
      setLoading(false);
    }
  };



  return (
    <div style={{
      padding: isMobile ? '12px' : '24px',
      maxWidth: '1200px',
      margin: '0 auto',
      width: '100%'
    }}>
      <Card title="🔍 智能搜索" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Search
              placeholder="输入您想要搜索的内容，例如：如何使用React、Python数据分析、项目管理方法..."
              allowClear
              enterButton={
                <Button type="primary" icon={<SearchOutlined />} size="large">
                  智能搜索
                </Button>
              }
              size="large"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onSearch={handleSearch}
              loading={loading}
              style={{
                width: '100%',
                fontSize: '16px'
              }}
            />

            <div style={{
              background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
              borderRadius: '8px',
              padding: '16px',
              marginTop: '12px'
            }}>
              <Text type="secondary" style={{ fontSize: '13px' }}>
                💡 <strong>搜索提示：</strong>
                支持自然语言搜索，如"如何配置数据库"、"React组件开发"、"项目部署流程"等
              </Text>
            </div>
          </div>

          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={24} md={12} lg={12}>
              <div>
                <Text strong style={{ fontSize: '14px' }}>返回结果数量: {topK}</Text>
                <Slider
                  min={1}
                  max={20}
                  value={topK}
                  onChange={setTopK}
                  style={{ marginTop: '8px' }}
                  marks={isMobile ? {
                    1: '1',
                    10: '10',
                    20: '20'
                  } : {
                    1: '1',
                    5: '5',
                    10: '10',
                    20: '20'
                  }}
                />
              </div>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <div style={{ textAlign: isMobile ? 'left' : 'right' }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  💡 提示：使用自然语言描述您要查找的内容
                </Text>
              </div>
            </Col>
          </Row>
        </Space>
      </Card>

      {loading && (
        <Card style={{ marginTop: '20px' }}>
          <div style={{ textAlign: 'center', padding: '60px 40px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '20px' }}>
              <Text style={{ fontSize: '16px' }}>🔍 正在智能搜索中...</Text>
            </div>
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">正在分析文档内容，请稍候</Text>
            </div>
          </div>
        </Card>
      )}

      {!loading && hasSearched && (
        <Card
          style={{ marginTop: '20px' }}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <span style={{ fontSize: '18px' }}>📊 搜索结果</span>
                {results.length > 0 && (
                  <span style={{ marginLeft: '12px', fontSize: '14px', color: '#666' }}>
                    共找到 {results.length} 个相关结果
                  </span>
                )}
              </div>
              {searchTime > 0 && (
                <Tag icon={<ClockCircleOutlined />} color="blue" style={{ fontSize: '12px' }}>
                  {(searchTime * 1000).toFixed(0)}ms
                </Tag>
              )}
            </div>
          }
        >
          {results.length === 0 ? (
            <div style={{ padding: '40px', textAlign: 'center' }}>
              <Empty
                description={
                  <div>
                    <div style={{ fontSize: '16px', marginBottom: '8px' }}>
                      没有找到相关内容
                    </div>
                    <div style={{ fontSize: '14px', color: '#666' }}>
                      💡 建议：
                    </div>
                    <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                      • 尝试使用不同的关键词<br/>
                      • 检查是否已上传相关文档<br/>
                      • 使用更简洁的描述
                    </div>
                  </div>
                }
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </div>
          ) : (
            <div>
              {results.map((item, index) => (
                <SearchResultCard
                  key={`${item.document_id}-${item.chunk_index}`}
                  result={item}
                  query={query}
                  index={index}
                />
              ))}
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default SearchInterface;
