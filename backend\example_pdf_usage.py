#!/usr/bin/env python3
"""
PDF解析功能使用示例
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from app.services.document_processor import DocumentProcessor
from app.models.schemas import FileType
from app.core.pdf_config import PDFExtractionConfig, PDF_CONFIG_TEMPLATES

async def basic_usage_example():
    """基本使用示例"""
    print("📚 基本PDF解析示例")
    print("=" * 50)
    
    # 创建文档处理器
    processor = DocumentProcessor()
    
    # 假设有一个PDF文件ID
    file_id = "example_document"
    
    try:
        # 提取文本
        text = await processor.extract_text_from_file(file_id, FileType.PDF)
        
        print(f"✅ 解析成功!")
        print(f"📊 提取文本长度: {len(text)} 字符")
        print(f"📝 内容预览: {text[:200]}...")
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")

async def custom_config_example():
    """自定义配置示例"""
    print("\n🔧 自定义配置示例")
    print("=" * 50)
    
    # 使用快速配置模板
    fast_config = PDF_CONFIG_TEMPLATES['fast']
    processor_fast = DocumentProcessor(pdf_config=fast_config)
    
    # 使用精确配置模板
    accurate_config = PDF_CONFIG_TEMPLATES['accurate']
    processor_accurate = DocumentProcessor(pdf_config=accurate_config)
    
    # 自定义配置
    custom_config = PDFExtractionConfig(
        MIN_TEXT_LENGTH=50,
        HIGH_QUALITY_THRESHOLD=0.8,
        ACCEPTABLE_QUALITY_THRESHOLD=0.5
    )
    processor_custom = DocumentProcessor(pdf_config=custom_config)
    
    print("✅ 创建了3个不同配置的处理器:")
    print("   - 快速模式: 降低质量要求，提升速度")
    print("   - 精确模式: 提高质量要求，确保准确性")
    print("   - 自定义模式: 根据需求定制参数")

async def analyze_pdf_structure_example():
    """PDF结构分析示例"""
    print("\n🔍 PDF结构分析示例")
    print("=" * 50)
    
    processor = DocumentProcessor()
    
    # 查找一个PDF文件进行分析
    upload_dir = Path(processor.settings.upload_path)
    pdf_files = list(upload_dir.glob("*.pdf"))
    
    if pdf_files:
        pdf_file = pdf_files[0]
        print(f"📄 分析文件: {pdf_file.name}")
        
        # 分析PDF结构
        pdf_info = processor._analyze_pdf_structure(str(pdf_file))
        
        print("📊 PDF结构信息:")
        for key, value in pdf_info.items():
            print(f"   {key}: {value}")
        
        # 获取推荐的解析方法
        methods = processor._get_optimal_extraction_methods(pdf_info)
        print(f"\n🎯 推荐解析方法:")
        for i, method in enumerate(methods, 1):
            print(f"   {i}. {method['name']}")
    else:
        print("❌ 未找到PDF文件")

async def quality_assessment_example():
    """文本质量评估示例"""
    print("\n📏 文本质量评估示例")
    print("=" * 50)
    
    processor = DocumentProcessor()
    
    # 示例文本
    test_texts = [
        "这是一段正常的中文文本，包含标点符号和数字123。",
        "Normal English text with punctuation and numbers 456.",
        "/G21 /G22 /G23 /G24 乱码文本 with special chars @@##$$",
        "A" * 10,  # 过短文本
        "这是一段很长的文本" * 50  # 长文本
    ]
    
    for i, text in enumerate(test_texts, 1):
        quality_score = processor._assess_text_quality_enhanced(text)
        preview = text[:50] + "..." if len(text) > 50 else text
        
        print(f"文本 {i}: {preview}")
        print(f"   质量评分: {quality_score:.3f}")
        
        if quality_score > 0.75:
            print("   ✅ 质量优秀")
        elif quality_score > 0.4:
            print("   ⚠️ 质量良好")
        else:
            print("   ❌ 质量较差")
        print()

async def batch_processing_example():
    """批量处理示例"""
    print("\n📦 批量处理示例")
    print("=" * 50)
    
    processor = DocumentProcessor()
    
    # 查找所有PDF文件
    upload_dir = Path(processor.settings.upload_path)
    pdf_files = list(upload_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 未找到PDF文件")
        return
    
    print(f"📁 找到 {len(pdf_files)} 个PDF文件")
    
    results = []
    
    # 批量处理
    for pdf_file in pdf_files[:3]:  # 限制处理前3个文件
        print(f"\n📄 处理: {pdf_file.name}")
        
        try:
            # 分析结构
            pdf_info = processor._analyze_pdf_structure(str(pdf_file))
            
            # 提取文本
            text = await processor.extract_text_from_file(
                pdf_file.stem, FileType.PDF
            )
            
            # 评估质量
            quality = processor._assess_text_quality_enhanced(text)
            
            result = {
                'filename': pdf_file.name,
                'success': True,
                'text_length': len(text),
                'quality_score': quality,
                'pdf_info': pdf_info
            }
            
            print(f"   ✅ 成功 - 长度: {len(text)}, 质量: {quality:.3f}")
            
        except Exception as e:
            result = {
                'filename': pdf_file.name,
                'success': False,
                'error': str(e)
            }
            print(f"   ❌ 失败: {e}")
        
        results.append(result)
    
    # 统计结果
    successful = sum(1 for r in results if r['success'])
    total_length = sum(r.get('text_length', 0) for r in results if r['success'])
    avg_quality = sum(r.get('quality_score', 0) for r in results if r['success']) / max(successful, 1)
    
    print(f"\n📊 批量处理结果:")
    print(f"   成功率: {successful}/{len(results)} ({successful/len(results)*100:.1f}%)")
    print(f"   总文本长度: {total_length:,} 字符")
    print(f"   平均质量: {avg_quality:.3f}")

async def error_handling_example():
    """错误处理示例"""
    print("\n🚨 错误处理示例")
    print("=" * 50)
    
    processor = DocumentProcessor()
    
    # 测试不存在的文件
    try:
        await processor.extract_text_from_file("nonexistent_file", FileType.PDF)
    except FileNotFoundError as e:
        print(f"✅ 正确捕获文件不存在错误: {e}")
    except Exception as e:
        print(f"❌ 意外错误: {e}")
    
    # 测试无效文件类型
    try:
        await processor.extract_text_from_file("test", "invalid_type")
    except Exception as e:
        print(f"✅ 正确捕获无效类型错误: {type(e).__name__}")

async def main():
    """主函数"""
    print("🚀 PDF解析功能使用示例")
    print("=" * 60)
    
    # 运行所有示例
    await basic_usage_example()
    await custom_config_example()
    await analyze_pdf_structure_example()
    await quality_assessment_example()
    await batch_processing_example()
    await error_handling_example()
    
    print("\n🎉 所有示例运行完成!")

if __name__ == "__main__":
    asyncio.run(main())
