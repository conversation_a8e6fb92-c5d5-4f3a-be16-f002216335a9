"""
应用程序配置设置
"""
import os
from typing import List
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Settings(BaseSettings):
    """应用程序设置"""
    
    # 应用基本信息
    app_name: str = "Knowledge Base System"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # OpenAI配置
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    openai_api_base_url: str = os.getenv("OPENAI_API_BASE_URL", "https://api.openai.com/v1")
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 文件存储配置
    upload_path: str = "./data"
    vector_store_path: str = "./vector_store"
    max_file_size: int = 50  # MB
    
    # CORS配置
    allowed_origins: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",  # Vite默认端口
        "http://127.0.0.1:5173"
    ]
    
    # LlamaIndex配置
    chunk_size: int = 1024
    chunk_overlap: int = 200
    similarity_top_k: int = 5
    
    # 支持的文件类型
    supported_file_types: List[str] = [
        ".txt", ".md", ".markdown", ".pdf", ".docx", ".doc"
    ]
    
    # 本地LLM配置（可选）
    use_local_llm: bool = False
    ollama_base_url: str = "http://localhost:11434"
    local_model_name: str = "llama2"

    # 嵌入模型配置
    use_local_embedding: bool = False  # 暂时禁用本地嵌入模型，使用简单嵌入模型
    local_embedding_model: str = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"  # 支持中文的多语言模型
    force_local_embedding: bool = False  # 强制使用本地嵌入模型
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }


# 创建全局设置实例
settings = Settings()


def get_settings() -> Settings:
    """获取应用设置"""
    return settings


# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    os.makedirs(settings.upload_path, exist_ok=True)
    os.makedirs(settings.vector_store_path, exist_ok=True)


# 验证配置
def validate_config():
    """验证配置是否正确"""
    # 注释掉严格的验证，因为我们有简单嵌入模型作为备用方案
    # if not settings.openai_api_key and not settings.use_local_llm and not settings.use_local_embedding:
    #     raise ValueError(
    #         "必须设置OPENAI_API_KEY或启用本地LLM/嵌入模型"
    #     )

    ensure_directories()
    return True
