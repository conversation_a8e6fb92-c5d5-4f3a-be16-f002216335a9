/**
 * 知识库系统主应用组件
 */
import React, { useState, useEffect } from 'react';
import { Layout, Menu, Typography, message, Spin, Button, Drawer } from 'antd';
import {
  UploadOutlined,
  SearchOutlined,
  FileTextOutlined,
  DashboardOutlined,
  GithubOutlined,
  MenuOutlined
} from '@ant-design/icons';
import FileUpload from './components/FileUpload';
import SearchInterface from './components/SearchInterface';
import DocumentManager from './components/DocumentManager';
import TestSearch from './components/TestSearch';
import { getSystemStatus, healthCheck } from './services/api';
import './App.css';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

function App() {
  const [selectedKey, setSelectedKey] = useState('search');
  const [systemStatus, setSystemStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 菜单项配置
  const menuItems = [
    {
      key: 'search',
      icon: <SearchOutlined />,
      label: '智能搜索',
    },
    {
      key: 'upload',
      icon: <UploadOutlined />,
      label: '文档上传',
    },
    {
      key: 'documents',
      icon: <FileTextOutlined />,
      label: '文档管理',
    },
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '系统状态',
    },
  ];

  // 加载系统状态
  const loadSystemStatus = async () => {
    try {
      // 先检查健康状态
      await healthCheck();

      // 获取系统状态
      const status = await getSystemStatus();
      setSystemStatus(status);
    } catch (error) {
      message.error('无法连接到后端服务，请确保服务已启动');
      console.error('系统状态加载失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理上传成功
  const handleUploadSuccess = (result) => {
    message.success('文档上传成功，正在处理中...');
    // 如果当前在文档管理页面，可以刷新列表
    if (selectedKey === 'documents') {
      // 这里可以触发文档列表刷新
    }
  };

  // 渲染内容区域
  const renderContent = () => {
    switch (selectedKey) {
      case 'search':
        return <SearchInterface />;
      case 'upload':
        return <FileUpload onUploadSuccess={handleUploadSuccess} />;
      case 'documents':
        return <DocumentManager />;
      case 'dashboard':
        return (
          <div style={{ padding: '20px' }}>
            <Title level={2}>📊 系统状态</Title>
            {systemStatus ? (
              <div>
                <p><Text strong>状态:</Text> {systemStatus.status}</p>
                <p><Text strong>总文档数:</Text> {systemStatus.total_documents}</p>
                <p><Text strong>已索引文档:</Text> {systemStatus.indexed_documents}</p>
                <p><Text strong>向量存储大小:</Text> {systemStatus.vector_store_size}</p>
                <p><Text strong>运行时间:</Text> {systemStatus.uptime}</p>
              </div>
            ) : (
              <Text type="secondary">加载中...</Text>
            )}
          </div>
        );

      default:
        return <SearchInterface />;
    }
  };

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth <= 576; // 只有真正的移动设备才算移动端
      setIsMobile(mobile);
      // 桌面端和平板都显示正常侧边栏
      setCollapsed(false);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  useEffect(() => {
    loadSystemStatus();
  }, []);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <Spin size="large" />
        <div style={{ marginLeft: '16px' }}>
          <Text>正在连接知识库系统...</Text>
        </div>
      </div>
    );
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{
        display: 'flex',
        alignItems: 'center',
        background: '#001529',
        padding: '0 24px',
        position: 'relative',
        zIndex: 1001
      }}>
        {isMobile && (
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => setMobileMenuVisible(true)}
            style={{
              color: 'white',
              marginRight: '12px',
              border: 'none',
              boxShadow: 'none'
            }}
          />
        )}
        <div style={{
          color: 'white',
          fontSize: '20px',
          fontWeight: 'bold',
          flex: 1
        }}>
          🧠 智能知识库系统
        </div>
        <div style={{
          color: 'white',
          display: isMobile ? 'none' : 'block'
        }}>
          <Text style={{ color: 'white', marginRight: '16px' }}>
            基于 LlamaIndex 构建
          </Text>
          <GithubOutlined style={{ fontSize: '18px', color: 'white' }} />
        </div>
      </Header>

      <Layout>
        {!isMobile ? (
          <Sider
            width={200}
            style={{ background: '#fff' }}
            collapsible={!isMobile}
            collapsed={collapsed && !isMobile}
            onCollapse={setCollapsed}
          >
            <Menu
              mode="inline"
              selectedKeys={[selectedKey]}
              style={{ height: '100%', borderRight: 0 }}
              items={menuItems}
              onClick={({ key }) => setSelectedKey(key)}
              inlineCollapsed={collapsed && !isMobile}
            />
          </Sider>
        ) : (
          <Drawer
            title="菜单"
            placement="left"
            onClose={() => setMobileMenuVisible(false)}
            open={mobileMenuVisible}
            bodyStyle={{ padding: 0 }}
            width={250}
          >
            <Menu
              mode="inline"
              selectedKeys={[selectedKey]}
              style={{ height: '100%', borderRight: 0 }}
              items={menuItems}
              onClick={({ key }) => {
                setSelectedKey(key);
                setMobileMenuVisible(false);
              }}
            />
          </Drawer>
        )}

        <Layout style={{ padding: '0' }}>
          <Content style={{
            background: '#fff',
            margin: 0,
            minHeight: 'calc(100vh - 64px)',
            overflow: 'auto'
          }}>
            {renderContent()}
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
}

export default App;
