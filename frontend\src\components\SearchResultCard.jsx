/**
 * 搜索结果卡片组件
 */
import React, { useState } from 'react';
import {
  Card,
  Tag,
  Typography,
  Row,
  Col,
  Button,
  Modal,
  Descriptions,
  Progress,
  Space,
  Tooltip
} from 'antd';
import {
  FileTextOutlined,
  EyeOutlined,
  CopyOutlined,
  DownloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Text, Paragraph, Title } = Typography;

const SearchResultCard = ({ result, query, index }) => {
  const [detailVisible, setDetailVisible] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测屏幕尺寸
  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 576); // 只有真正的移动设备才算移动端
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // 高亮搜索关键词
  const highlightText = (text, query) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} style={{ 
          backgroundColor: '#fff566', 
          padding: '0 2px',
          borderRadius: '2px',
          fontWeight: 'bold'
        }}>
          {part}
        </mark>
      ) : part
    );
  };

  // 获取相似度颜色和描述
  const getScoreInfo = (score) => {
    if (score >= 0.9) return { color: 'green', text: '极高相关', desc: '内容高度匹配' };
    if (score >= 0.8) return { color: 'blue', text: '高相关', desc: '内容很匹配' };
    if (score >= 0.6) return { color: 'orange', text: '中等相关', desc: '内容部分匹配' };
    if (score >= 0.4) return { color: 'red', text: '低相关', desc: '内容略微匹配' };
    return { color: 'default', text: '弱相关', desc: '内容关联较弱' };
  };

  // 获取文件类型信息
  const getFileTypeInfo = (fileType) => {
    const typeMap = {
      txt: { icon: '📄', color: 'blue', name: '文本文档' },
      md: { icon: '📝', color: 'green', name: 'Markdown' },
      markdown: { icon: '📝', color: 'green', name: 'Markdown' },
      pdf: { icon: '📕', color: 'red', name: 'PDF文档' },
      docx: { icon: '📘', color: 'blue', name: 'Word文档' },
      doc: { icon: '📘', color: 'blue', name: 'Word文档' }
    };
    
    return typeMap[fileType?.toLowerCase()] || { icon: '📄', color: 'default', name: '未知格式' };
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (!bytes) return '未知';
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 复制内容到剪贴板
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      // 这里可以添加成功提示
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const scoreInfo = getScoreInfo(result.score);
  const fileTypeInfo = getFileTypeInfo(result.metadata?.file_type);
  const contentPreview = expanded ? result.content : result.content.substring(0, 300);
  const needsExpansion = result.content.length > 300;

  return (
    <>
      <Card
        size="small"
        style={{ 
          marginBottom: '16px',
          border: '1px solid #e8e8e8',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
          transition: 'all 0.3s ease'
        }}
        hoverable
        title={
          <Row justify="space-between" align="middle" wrap={isMobile}>
            <Col xs={24} sm={16} md={16}>
              <Space wrap>
                <span style={{ fontSize: '18px' }}>{fileTypeInfo.icon}</span>
                <Text strong style={{ fontSize: isMobile ? '14px' : '16px' }}>
                  {isMobile && result.document_name.length > 20
                    ? result.document_name.substring(0, 20) + '...'
                    : result.document_name}
                </Text>
                <Tag color={fileTypeInfo.color} size="small">
                  {fileTypeInfo.name}
                </Tag>
              </Space>
            </Col>
            <Col xs={24} sm={8} md={8}>
              <Space wrap style={{ justifyContent: isMobile ? 'flex-start' : 'flex-end', width: '100%' }}>
                <Tooltip title={scoreInfo.desc}>
                  <Tag color={scoreInfo.color} style={{ fontSize: isMobile ? '11px' : '12px' }}>
                    {isMobile ? `${(result.score * 100).toFixed(0)}%` : `${scoreInfo.text} ${(result.score * 100).toFixed(1)}%`}
                  </Tag>
                </Tooltip>
                <Tag color="blue" style={{ fontSize: isMobile ? '11px' : '12px' }}>
                  第 {result.chunk_index + 1} 段
                </Tag>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  #{index + 1}
                </Text>
              </Space>
            </Col>
          </Row>
        }
        extra={
          <Space>
            <Tooltip title="查看详情">
              <Button 
                type="text" 
                icon={<InfoCircleOutlined />}
                onClick={() => setDetailVisible(true)}
              />
            </Tooltip>
            <Tooltip title="复制内容">
              <Button 
                type="text" 
                icon={<CopyOutlined />}
                onClick={() => copyToClipboard(result.content)}
              />
            </Tooltip>
          </Space>
        }
      >
        {/* 相似度进度条 */}
        <div style={{ marginBottom: '12px' }}>
          <Progress
            percent={result.score * 100}
            size="small"
            strokeColor={{
              '0%': '#ff4d4f',
              '50%': '#faad14',
              '100%': '#52c41a',
            }}
            showInfo={false}
          />
        </div>

        {/* 内容展示 */}
        <Paragraph style={{ 
          marginBottom: '16px',
          lineHeight: '1.6',
          fontSize: '14px'
        }}>
          {highlightText(contentPreview, query)}
          {needsExpansion && !expanded && '...'}
        </Paragraph>

        {/* 展开/收起按钮 */}
        {needsExpansion && (
          <div style={{ textAlign: 'center', marginBottom: '12px' }}>
            <Button 
              type="link" 
              size="small"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? '收起' : '展开全文'}
            </Button>
          </div>
        )}

        {/* 元数据信息 */}
        <Row gutter={[8, 8]} style={{
          paddingTop: '12px',
          borderTop: '1px solid #f0f0f0',
          fontSize: '12px'
        }}>
          <Col xs={12} sm={6}>
            <Text type="secondary">文档ID:</Text>
            <br />
            <Text code style={{ fontSize: '11px' }}>
              {result.document_id.substring(0, 8)}...
            </Text>
          </Col>
          <Col xs={12} sm={6}>
            <Text type="secondary">文件大小:</Text>
            <br />
            <Text>{formatFileSize(result.metadata?.file_size)}</Text>
          </Col>
          <Col xs={12} sm={6}>
            <Text type="secondary">上传时间:</Text>
            <br />
            <Text>
              {result.metadata?.upload_time ?
                new Date(result.metadata.upload_time).toLocaleDateString('zh-CN') :
                '未知'
              }
            </Text>
          </Col>
          <Col xs={12} sm={6}>
            <Text type="secondary">内容长度:</Text>
            <br />
            <Text>{result.content.length} 字符</Text>
          </Col>
        </Row>
      </Card>

      {/* 详情模态框 */}
      <Modal
        title={
          <Space>
            <span style={{ fontSize: '20px' }}>{fileTypeInfo.icon}</span>
            <span>文档详情</span>
          </Space>
        }
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="copy" icon={<CopyOutlined />} onClick={() => copyToClipboard(result.content)} size={isMobile ? 'small' : 'middle'}>
            复制内容
          </Button>,
          <Button key="close" type="primary" onClick={() => setDetailVisible(false)} size={isMobile ? 'small' : 'middle'}>
            关闭
          </Button>
        ]}
        width={isMobile ? '95%' : 800}
        style={isMobile ? { top: 20 } : {}}
      >
        <Descriptions column={2} bordered size="small">
          <Descriptions.Item label="文档名称">{result.document_name}</Descriptions.Item>
          <Descriptions.Item label="文件类型">{fileTypeInfo.name}</Descriptions.Item>
          <Descriptions.Item label="相似度分数">{(result.score * 100).toFixed(2)}%</Descriptions.Item>
          <Descriptions.Item label="内容段落">第 {result.chunk_index + 1} 段</Descriptions.Item>
          <Descriptions.Item label="文档ID">{result.document_id}</Descriptions.Item>
          <Descriptions.Item label="文件大小">{formatFileSize(result.metadata?.file_size)}</Descriptions.Item>
        </Descriptions>

        <div style={{ marginTop: '16px' }}>
          <Title level={5}>匹配内容:</Title>
          <div style={{
            background: '#f5f5f5',
            padding: '16px',
            borderRadius: '6px',
            maxHeight: '400px',
            overflow: 'auto',
            lineHeight: '1.6'
          }}>
            {highlightText(result.content, query)}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default SearchResultCard;
