"""
文档处理服务
"""
import os
import uuid
import asyncio
from typing import List, Optional, Dict, Any
from pathlib import Path
import aiofiles

# 文档处理库
import PyPDF2
import pdfplumber
from docx import Document
import markdown

from app.core.config import get_settings
from app.models.schemas import DocumentInfo, FileType, DocumentStatus

settings = get_settings()


class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self):
        self.settings = settings
        
    async def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """保存上传的文件"""
        # 生成唯一的文件ID
        file_id = str(uuid.uuid4())
        file_extension = Path(filename).suffix.lower()
        
        # 创建新的文件名
        new_filename = f"{file_id}{file_extension}"
        file_path = os.path.join(self.settings.upload_path, new_filename)
        
        # 异步保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
            
        return file_id
    
    def get_file_type(self, filename: str) -> Optional[FileType]:
        """根据文件扩展名确定文件类型"""
        extension = Path(filename).suffix.lower()
        
        type_mapping = {
            '.txt': FileType.TXT,
            '.md': FileType.MARKDOWN,
            '.markdown': FileType.MARKDOWN,
            '.pdf': FileType.PDF,
            '.docx': FileType.DOCX,
            '.doc': FileType.DOC
        }
        
        return type_mapping.get(extension)
    
    async def extract_text_from_file(self, file_id: str, file_type: FileType) -> str:
        """从文件中提取文本内容"""
        file_path = self._get_file_path(file_id, file_type)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            if file_type == FileType.TXT:
                return await self._extract_from_txt(file_path)
            elif file_type == FileType.MARKDOWN:
                return await self._extract_from_markdown(file_path)
            elif file_type == FileType.PDF:
                return await self._extract_from_pdf(file_path)
            elif file_type in [FileType.DOCX, FileType.DOC]:
                return await self._extract_from_docx(file_path)
            else:
                raise ValueError(f"不支持的文件类型: {file_type}")
                
        except Exception as e:
            raise Exception(f"文本提取失败: {str(e)}")
    
    def _get_file_path(self, file_id: str, file_type: FileType) -> str:
        """获取文件完整路径"""
        extension_map = {
            FileType.TXT: '.txt',
            FileType.MARKDOWN: '.md',
            FileType.PDF: '.pdf',
            FileType.DOCX: '.docx',
            FileType.DOC: '.doc'
        }
        
        filename = f"{file_id}{extension_map[file_type]}"
        return os.path.join(self.settings.upload_path, filename)
    
    async def _extract_from_txt(self, file_path: str) -> str:
        """从TXT文件提取文本"""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            return await f.read()
    
    async def _extract_from_markdown(self, file_path: str) -> str:
        """从Markdown文件提取文本"""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            md_content = await f.read()
            # 将Markdown转换为纯文本
            html = markdown.markdown(md_content)
            # 这里可以进一步处理HTML标签，暂时返回原始内容
            return md_content
    
    async def _extract_from_pdf(self, file_path: str) -> str:
        """从PDF文件提取文本（改进版，解决乱码问题）"""
        text_content = []

        # 尝试多种PDF解析方法
        methods = [
            self._extract_with_pdfplumber_advanced,
            self._extract_with_pdfplumber_basic,
            self._extract_with_pypdf2
        ]

        for method_name, method in zip(
            ["pdfplumber高级模式", "pdfplumber基础模式", "PyPDF2"],
            methods
        ):
            try:
                print(f"尝试使用 {method_name} 解析PDF...")
                result = method(file_path)
                if result and len(result.strip()) > 50:  # 确保提取到足够的内容
                    # 检查文本质量
                    quality_score = self._assess_text_quality(result)
                    print(f"{method_name} 文本质量评分: {quality_score:.2f}")

                    if quality_score > 0.6:  # 质量阈值
                        print(f"✅ {method_name} 解析成功，质量良好")
                        return result
                    else:
                        print(f"⚠️ {method_name} 解析质量较差，尝试下一种方法")
                        text_content.append(result)  # 保存作为备用
                else:
                    print(f"❌ {method_name} 提取内容不足")
            except Exception as e:
                print(f"❌ {method_name} 失败: {e}")
                continue

        # 如果所有方法都失败或质量不佳，返回最好的结果
        if text_content:
            print("⚠️ 使用质量较差的解析结果")
            return max(text_content, key=len)  # 返回最长的结果
        else:
            raise Exception("PDF文件中未能提取到任何文本内容")

    def _extract_with_pdfplumber_advanced(self, file_path: str) -> str:
        """使用pdfplumber高级模式提取文本"""
        text_content = []

        with pdfplumber.open(file_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                page_text = []

                # 尝试不同的文本提取策略
                strategies = [
                    lambda p: p.extract_text(x_tolerance=3, y_tolerance=3),
                    lambda p: p.extract_text(x_tolerance=1, y_tolerance=1),
                    lambda p: p.extract_text()
                ]

                for strategy in strategies:
                    try:
                        text = strategy(page)
                        if text and len(text.strip()) > 10:
                            cleaned = self._clean_pdf_text(text)
                            if cleaned and self._assess_text_quality(cleaned) > 0.5:
                                page_text.append(cleaned)
                                break
                    except:
                        continue

                # 如果文本提取失败，尝试提取表格
                if not page_text:
                    try:
                        tables = page.extract_tables()
                        if tables:
                            table_text = self._extract_table_text(tables)
                            if table_text:
                                page_text.append(table_text)
                    except:
                        pass

                if page_text:
                    text_content.append(f"[第{page_num + 1}页]\n{page_text[0]}")

        return '\n\n'.join(text_content)

    def _extract_with_pdfplumber_basic(self, file_path: str) -> str:
        """使用pdfplumber基础模式提取文本"""
        text_content = []

        with pdfplumber.open(file_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                try:
                    text = page.extract_text()
                    if text and text.strip():
                        cleaned_text = self._clean_pdf_text(text)
                        if cleaned_text:
                            text_content.append(f"[第{page_num + 1}页]\n{cleaned_text}")
                except Exception as e:
                    print(f"页面 {page_num + 1} 基础提取失败: {e}")
                    continue

        return '\n\n'.join(text_content)

    def _extract_with_pypdf2(self, file_path: str) -> str:
        """使用PyPDF2提取文本"""
        text_content = []

        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    text = page.extract_text()
                    if text and text.strip():
                        cleaned_text = self._clean_pdf_text(text)
                        if cleaned_text:
                            text_content.append(f"[第{page_num + 1}页]\n{cleaned_text}")
                except Exception as e:
                    print(f"PyPDF2页面 {page_num + 1} 提取失败: {e}")
                    continue

        return '\n\n'.join(text_content)

    def _assess_text_quality(self, text: str) -> float:
        """评估文本质量（0-1分）"""
        if not text or len(text) < 10:
            return 0.0

        import re

        # 计算各种字符的比例
        total_chars = len(text)
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        digits = len(re.findall(r'\d', text))
        spaces = len(re.findall(r'\s', text))
        punctuation = len(re.findall(r'[.,;:!?()[\]{}"\'-]', text))

        # 计算可读字符比例
        readable_chars = chinese_chars + english_chars + digits + spaces + punctuation
        readable_ratio = readable_chars / total_chars if total_chars > 0 else 0

        # 计算特殊字符比例
        special_chars = total_chars - readable_chars
        special_ratio = special_chars / total_chars if total_chars > 0 else 0

        # 质量评分
        quality_score = readable_ratio - (special_ratio * 2)  # 特殊字符惩罚更重

        # 额外加分项
        if chinese_chars > 0:  # 包含中文
            quality_score += 0.1
        if english_chars > 0:  # 包含英文
            quality_score += 0.1
        if len(text) > 100:  # 内容充实
            quality_score += 0.1

        return max(0.0, min(1.0, quality_score))

    def _clean_pdf_text(self, text: str) -> str:
        """清理PDF提取的文本"""
        if not text:
            return ""

        import re

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除页眉页脚中的常见模式
        text = re.sub(r'^\d+\s*$', '', text, flags=re.MULTILINE)  # 单独的页码
        text = re.sub(r'^第\s*\d+\s*页\s*$', '', text, flags=re.MULTILINE)  # 页码标识

        # 修复常见的PDF提取问题
        text = text.replace('', ' ')  # 替换特殊空格
        text = text.replace('\x00', '')  # 移除空字符

        # 移除过短的行（可能是乱码）
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if len(line) > 2:  # 保留长度大于2的行
                cleaned_lines.append(line)

        result = '\n'.join(cleaned_lines).strip()

        # 如果结果太短或包含太多特殊字符，可能是乱码
        if len(result) < 10:
            return ""

        # 检查是否包含过多的特殊字符（可能是乱码）
        special_char_ratio = len(re.findall(r'[^\w\s\u4e00-\u9fff]', result)) / len(result) if result else 0
        if special_char_ratio > 0.5:  # 如果特殊字符超过50%，可能是乱码
            print(f"警告: 检测到可能的乱码文本，特殊字符比例: {special_char_ratio:.2%}")

        return result

    def _extract_table_text(self, tables: list) -> str:
        """从表格中提取文本"""
        table_texts = []

        for table_idx, table in enumerate(tables):
            if not table:
                continue

            table_content = []
            for row in table:
                if row:
                    # 过滤掉None值并转换为字符串
                    row_text = [str(cell).strip() if cell is not None else "" for cell in row]
                    # 只保留非空行
                    if any(cell for cell in row_text):
                        table_content.append(" | ".join(row_text))

            if table_content:
                table_text = f"表格 {table_idx + 1}:\n" + "\n".join(table_content)
                table_texts.append(table_text)

        return "\n\n".join(table_texts)

    async def _extract_from_docx(self, file_path: str) -> str:
        """从DOCX文件提取文本"""
        doc = Document(file_path)
        text_content = []
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_content.append(paragraph.text)
        
        return '\n\n'.join(text_content)
    
    async def delete_file(self, file_id: str, file_type: FileType) -> bool:
        """删除文件"""
        try:
            file_path = self._get_file_path(file_id, file_type)
            if os.path.exists(file_path):
                os.remove(file_path)
            return True
        except Exception:
            return False
    
    def get_content_preview(self, content: str, max_length: int = 200) -> str:
        """获取内容预览"""
        if len(content) <= max_length:
            return content
        return content[:max_length] + "..."
