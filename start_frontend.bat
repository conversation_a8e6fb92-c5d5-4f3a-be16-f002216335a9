@echo off
echo ========================================
echo 启动知识库系统前端界面
echo ========================================

cd frontend

echo 检查Node.js环境...
node --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js 16+
    pause
    exit /b 1
)

echo.
echo 检查npm...
npm --version
if %errorlevel% neq 0 (
    echo 错误: 未找到npm
    pause
    exit /b 1
)

echo.
echo 安装依赖包...
npm install

echo.
echo 启动开发服务器...
echo 前端将在 http://localhost:5173 启动
echo.

npm run dev

pause
