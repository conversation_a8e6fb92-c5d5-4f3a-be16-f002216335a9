"""
文档处理服务
"""
import os
import uuid
import asyncio
import logging
from typing import List, Optional, Dict, Any, Tuple
from pathlib import Path
import aiofiles
import re
import io

# 文档处理库
import PyPDF2
import pdfplumber
from docx import Document
import markdown

from app.core.config import get_settings
from app.models.schemas import DocumentInfo, FileType, DocumentStatus
from app.core.pdf_config import (
    DEFAULT_PDF_CONFIG,
    PDFMethodSelector,
    PDFQualityAssessor,
    PDFOptimizationStrategy
)

# 配置日志
logger = logging.getLogger(__name__)

settings = get_settings()


class DocumentProcessor:
    """文档处理器"""

    def __init__(self, pdf_config=None):
        self.settings = settings
        self.pdf_config = pdf_config or DEFAULT_PDF_CONFIG
        self.method_selector = PDFMethodSelector()
        self.quality_assessor = PDFQualityAssessor()
        self.optimization_strategy = PDFOptimizationStrategy()

        # 确保上传目录存在
        os.makedirs(self.settings.upload_path, exist_ok=True)
        
    async def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """保存上传的文件"""
        # 生成唯一的文件ID
        file_id = str(uuid.uuid4())
        file_extension = Path(filename).suffix.lower()
        
        # 创建新的文件名
        new_filename = f"{file_id}{file_extension}"
        file_path = os.path.join(self.settings.upload_path, new_filename)
        
        # 异步保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
            
        return file_id
    
    def get_file_type(self, filename: str) -> Optional[FileType]:
        """根据文件扩展名确定文件类型"""
        extension = Path(filename).suffix.lower()
        
        type_mapping = {
            '.txt': FileType.TXT,
            '.md': FileType.MARKDOWN,
            '.markdown': FileType.MARKDOWN,
            '.pdf': FileType.PDF,
            '.docx': FileType.DOCX,
            '.doc': FileType.DOC
        }
        
        return type_mapping.get(extension)
    
    async def extract_text_from_file(self, file_id: str, file_type: FileType) -> str:
        """从文件中提取文本内容"""
        file_path = self._get_file_path(file_id, file_type)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            if file_type == FileType.TXT:
                return await self._extract_from_txt(file_path)
            elif file_type == FileType.MARKDOWN:
                return await self._extract_from_markdown(file_path)
            elif file_type == FileType.PDF:
                return await self._extract_from_pdf(file_path)
            elif file_type in [FileType.DOCX, FileType.DOC]:
                return await self._extract_from_docx(file_path)
            else:
                raise ValueError(f"不支持的文件类型: {file_type}")
                
        except Exception as e:
            raise Exception(f"文本提取失败: {str(e)}")
    
    def _get_file_path(self, file_id: str, file_type: FileType) -> str:
        """获取文件完整路径"""
        extension_map = {
            FileType.TXT: '.txt',
            FileType.MARKDOWN: '.md',
            FileType.PDF: '.pdf',
            FileType.DOCX: '.docx',
            FileType.DOC: '.doc'
        }
        
        filename = f"{file_id}{extension_map[file_type]}"
        return os.path.join(self.settings.upload_path, filename)
    
    async def _extract_from_txt(self, file_path: str) -> str:
        """从TXT文件提取文本"""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            return await f.read()
    
    async def _extract_from_markdown(self, file_path: str) -> str:
        """从Markdown文件提取文本"""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            md_content = await f.read()
            # 将Markdown转换为纯文本
            html = markdown.markdown(md_content)
            # 这里可以进一步处理HTML标签，暂时返回原始内容
            return md_content
    
    async def _extract_from_pdf(self, file_path: str) -> str:
        """从PDF文件提取文本（增强版，支持多种解析策略）"""
        logger.info(f"开始解析PDF文件: {file_path}")

        # 首先检查PDF文件基本信息
        pdf_info = self._analyze_pdf_structure(file_path)
        logger.info(f"PDF信息: {pdf_info}")

        # 根据PDF特征选择最佳解析策略
        extraction_methods = self._get_optimal_extraction_methods(pdf_info)

        best_result = None
        best_score = 0.0
        all_results = []

        for method_info in extraction_methods:
            method_name = method_info['name']
            method_func = method_info['func']

            try:
                logger.info(f"尝试使用 {method_name} 解析PDF...")
                result = await method_func(file_path, pdf_info)

                if result and len(result.strip()) > getattr(self.pdf_config, 'MIN_TEXT_LENGTH', 30):
                    # 评估文本质量
                    quality_score = self._assess_text_quality_enhanced(result)
                    logger.info(f"{method_name} 文本质量评分: {quality_score:.3f}")

                    all_results.append({
                        'method': method_name,
                        'content': result,
                        'score': quality_score,
                        'length': len(result)
                    })

                    # 如果质量足够好，直接返回
                    if quality_score > getattr(self.pdf_config, 'HIGH_QUALITY_THRESHOLD', 0.75):
                        logger.info(f"✅ {method_name} 解析成功，质量优秀")
                        return result

                    # 记录最佳结果
                    if quality_score > best_score:
                        best_result = result
                        best_score = quality_score

                else:
                    logger.warning(f"❌ {method_name} 提取内容不足")

            except Exception as e:
                logger.error(f"❌ {method_name} 失败: {e}")
                continue

        # 选择最佳结果
        if best_result and best_score > getattr(self.pdf_config, 'ACCEPTABLE_QUALITY_THRESHOLD', 0.4):
            logger.info(f"✅ 使用最佳解析结果，质量评分: {best_score:.3f}")
            return best_result
        elif all_results:
            # 如果没有高质量结果，选择最长的结果
            longest_result = max(all_results, key=lambda x: x['length'])
            logger.warning(f"⚠️ 使用最长的解析结果: {longest_result['method']}")
            return longest_result['content']
        else:
            raise Exception("PDF文件中未能提取到任何有效文本内容")

    def _analyze_pdf_structure(self, file_path: str) -> Dict[str, Any]:
        """分析PDF文件结构和特征"""
        try:
            with pdfplumber.open(file_path) as pdf:
                info = {
                    'page_count': len(pdf.pages),
                    'has_text': False,
                    'has_images': False,
                    'has_tables': False,
                    'text_density': 0.0,
                    'avg_chars_per_page': 0,
                    'is_scanned': False,
                    'encoding_issues': False
                }

                # 分析前几页来判断PDF特征
                sample_pages = min(3, len(pdf.pages))
                total_chars = 0
                pages_with_text = 0

                for i in range(sample_pages):
                    page = pdf.pages[i]

                    # 检查文本内容
                    text = page.extract_text()
                    if text and text.strip():
                        info['has_text'] = True
                        total_chars += len(text)
                        pages_with_text += 1

                        # 检查编码问题
                        if self._has_encoding_issues(text):
                            info['encoding_issues'] = True

                    # 检查图片
                    if page.images:
                        info['has_images'] = True

                    # 检查表格
                    try:
                        tables = page.extract_tables()
                        if tables:
                            info['has_tables'] = True
                    except:
                        pass

                # 计算文本密度
                if pages_with_text > 0:
                    info['avg_chars_per_page'] = total_chars / pages_with_text
                    info['text_density'] = pages_with_text / sample_pages

                # 判断是否为扫描版PDF
                if info['has_images'] and not info['has_text']:
                    info['is_scanned'] = True
                elif info['text_density'] < 0.3:
                    info['is_scanned'] = True

                return info

        except Exception as e:
            logger.error(f"PDF结构分析失败: {e}")
            return {'page_count': 0, 'has_text': False, 'is_scanned': True}

    def _has_encoding_issues(self, text: str) -> bool:
        """检查文本是否有编码问题"""
        if not text:
            return False

        # 检查常见的编码问题标志
        encoding_indicators = [
            '\ufffd',  # 替换字符
            '?',       # 过多的问号
            '\x00',    # 空字符
        ]

        for indicator in encoding_indicators:
            if indicator in text:
                return True

        # 检查特殊字符比例
        special_chars = sum(1 for c in text if ord(c) > 127 and not '\u4e00' <= c <= '\u9fff')
        if len(text) > 0 and special_chars / len(text) > 0.3:
            return True

        return False

    def _get_optimal_extraction_methods(self, pdf_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据PDF特征选择最佳解析方法"""
        methods = []

        if pdf_info.get('is_scanned', False):
            # 扫描版PDF，优先使用OCR相关方法
            logger.info("检测到扫描版PDF，将使用OCR优化策略")
            methods.extend([
                {'name': 'pdfplumber图像优化模式', 'func': self._extract_with_pdfplumber_ocr_optimized},
                {'name': 'pdfplumber高级模式', 'func': self._extract_with_pdfplumber_advanced_async},
                {'name': 'PyPDF2兼容模式', 'func': self._extract_with_pypdf2_async}
            ])
        elif pdf_info.get('has_tables', False):
            # 包含表格的PDF
            logger.info("检测到表格内容，将使用表格优化策略")
            methods.extend([
                {'name': 'pdfplumber表格优化模式', 'func': self._extract_with_pdfplumber_table_optimized},
                {'name': 'pdfplumber高级模式', 'func': self._extract_with_pdfplumber_advanced_async},
                {'name': 'pdfplumber基础模式', 'func': self._extract_with_pdfplumber_basic_async}
            ])
        elif pdf_info.get('encoding_issues', False):
            # 有编码问题的PDF
            logger.info("检测到编码问题，将使用编码优化策略")
            methods.extend([
                {'name': 'pdfplumber编码优化模式', 'func': self._extract_with_pdfplumber_encoding_optimized},
                {'name': 'PyPDF2编码修复模式', 'func': self._extract_with_pypdf2_encoding_fixed},
                {'name': 'pdfplumber基础模式', 'func': self._extract_with_pdfplumber_basic_async}
            ])
        else:
            # 标准文本PDF
            logger.info("检测到标准文本PDF，将使用标准策略")
            methods.extend([
                {'name': 'pdfplumber高级模式', 'func': self._extract_with_pdfplumber_advanced_async},
                {'name': 'pdfplumber基础模式', 'func': self._extract_with_pdfplumber_basic_async},
                {'name': 'PyPDF2标准模式', 'func': self._extract_with_pypdf2_async}
            ])

        return methods

    async def _extract_with_pdfplumber_advanced_async(self, file_path: str, pdf_info: Dict[str, Any]) -> str:
        """使用pdfplumber高级模式提取文本（异步版本）"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self._extract_with_pdfplumber_advanced_sync, file_path, pdf_info
        )

    def _extract_with_pdfplumber_advanced_sync(self, file_path: str, pdf_info: Dict[str, Any]) -> str:
        """使用pdfplumber高级模式提取文本（同步版本）"""
        text_content = []

        with pdfplumber.open(file_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                page_text = []

                # 根据PDF特征调整提取策略
                if pdf_info.get('text_density', 0) > 0.8:
                    # 高文本密度，使用精确模式
                    strategies = [
                        lambda p: p.extract_text(x_tolerance=1, y_tolerance=1),
                        lambda p: p.extract_text(x_tolerance=2, y_tolerance=2),
                        lambda p: p.extract_text()
                    ]
                else:
                    # 低文本密度，使用宽松模式
                    strategies = [
                        lambda p: p.extract_text(x_tolerance=3, y_tolerance=3),
                        lambda p: p.extract_text(x_tolerance=5, y_tolerance=5),
                        lambda p: p.extract_text()
                    ]

                for strategy in strategies:
                    try:
                        text = strategy(page)
                        if text and len(text.strip()) > 10:
                            cleaned = self._clean_pdf_text_enhanced(text)
                            if cleaned and self._assess_text_quality_enhanced(cleaned) > 0.5:
                                page_text.append(cleaned)
                                break
                    except Exception as e:
                        logger.debug(f"策略失败: {e}")
                        continue

                # 如果文本提取失败，尝试提取表格
                if not page_text and pdf_info.get('has_tables', False):
                    try:
                        tables = page.extract_tables()
                        if tables:
                            table_text = self._extract_table_text_enhanced(tables)
                            if table_text:
                                page_text.append(table_text)
                    except Exception as e:
                        logger.debug(f"表格提取失败: {e}")

                if page_text:
                    text_content.append(f"[第{page_num + 1}页]\n{page_text[0]}")

        return '\n\n'.join(text_content)

    async def _extract_with_pdfplumber_basic_async(self, file_path: str, pdf_info: Dict[str, Any]) -> str:
        """使用pdfplumber基础模式提取文本（异步版本）"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self._extract_with_pdfplumber_basic_sync, file_path
        )

    def _extract_with_pdfplumber_basic_sync(self, file_path: str) -> str:
        """使用pdfplumber基础模式提取文本（同步版本）"""
        text_content = []

        with pdfplumber.open(file_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                try:
                    text = page.extract_text()
                    if text and text.strip():
                        cleaned_text = self._clean_pdf_text_enhanced(text)
                        if cleaned_text:
                            text_content.append(f"[第{page_num + 1}页]\n{cleaned_text}")
                except Exception as e:
                    logger.debug(f"页面 {page_num + 1} 基础提取失败: {e}")
                    continue

        return '\n\n'.join(text_content)

    async def _extract_with_pypdf2_async(self, file_path: str, pdf_info: Dict[str, Any]) -> str:
        """使用PyPDF2提取文本（异步版本）"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self._extract_with_pypdf2_sync, file_path
        )

    def _extract_with_pypdf2_sync(self, file_path: str) -> str:
        """使用PyPDF2提取文本（同步版本）"""
        text_content = []

        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    text = page.extract_text()
                    if text and text.strip():
                        cleaned_text = self._clean_pdf_text_enhanced(text)
                        if cleaned_text:
                            text_content.append(f"[第{page_num + 1}页]\n{cleaned_text}")
                except Exception as e:
                    logger.debug(f"PyPDF2页面 {page_num + 1} 提取失败: {e}")
                    continue

        return '\n\n'.join(text_content)

    async def _extract_with_pdfplumber_table_optimized(self, file_path: str, pdf_info: Dict[str, Any]) -> str:
        """使用pdfplumber表格优化模式提取文本"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self._extract_with_pdfplumber_table_optimized_sync, file_path
        )

    def _extract_with_pdfplumber_table_optimized_sync(self, file_path: str) -> str:
        """表格优化模式（同步版本）"""
        text_content = []

        with pdfplumber.open(file_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                page_content = []

                # 首先尝试提取常规文本
                try:
                    text = page.extract_text()
                    if text and text.strip():
                        cleaned_text = self._clean_pdf_text_enhanced(text)
                        if cleaned_text:
                            page_content.append(cleaned_text)
                except:
                    pass

                # 专门提取表格
                try:
                    tables = page.extract_tables()
                    if tables:
                        table_text = self._extract_table_text_enhanced(tables)
                        if table_text:
                            page_content.append(f"\n{table_text}")
                except:
                    pass

                if page_content:
                    text_content.append(f"[第{page_num + 1}页]\n" + "\n".join(page_content))

        return '\n\n'.join(text_content)

    async def _extract_with_pdfplumber_ocr_optimized(self, file_path: str, pdf_info: Dict[str, Any]) -> str:
        """使用pdfplumber OCR优化模式提取文本"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self._extract_with_pdfplumber_ocr_optimized_sync, file_path
        )

    def _extract_with_pdfplumber_ocr_optimized_sync(self, file_path: str) -> str:
        """OCR优化模式（同步版本）"""
        text_content = []

        with pdfplumber.open(file_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                page_text = []

                # 对于扫描版PDF，尝试更宽松的参数
                strategies = [
                    lambda p: p.extract_text(x_tolerance=5, y_tolerance=5),
                    lambda p: p.extract_text(x_tolerance=10, y_tolerance=10),
                    lambda p: p.extract_text(layout=True),
                    lambda p: p.extract_text()
                ]

                for strategy in strategies:
                    try:
                        text = strategy(page)
                        if text and len(text.strip()) > 5:
                            cleaned = self._clean_pdf_text_enhanced(text)
                            if cleaned:
                                page_text.append(cleaned)
                                break
                    except:
                        continue

                if page_text:
                    text_content.append(f"[第{page_num + 1}页]\n{page_text[0]}")

        return '\n\n'.join(text_content)

    async def _extract_with_pdfplumber_encoding_optimized(self, file_path: str, pdf_info: Dict[str, Any]) -> str:
        """使用pdfplumber编码优化模式提取文本"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self._extract_with_pdfplumber_encoding_optimized_sync, file_path
        )

    def _extract_with_pdfplumber_encoding_optimized_sync(self, file_path: str) -> str:
        """编码优化模式（同步版本）"""
        text_content = []

        with pdfplumber.open(file_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                try:
                    # 尝试不同的编码处理方式
                    text = page.extract_text()
                    if text:
                        # 特殊的编码修复处理
                        cleaned_text = self._fix_encoding_issues(text)
                        if cleaned_text and len(cleaned_text.strip()) > 10:
                            text_content.append(f"[第{page_num + 1}页]\n{cleaned_text}")
                except Exception as e:
                    logger.debug(f"编码优化页面 {page_num + 1} 失败: {e}")
                    continue

        return '\n\n'.join(text_content)

    async def _extract_with_pypdf2_encoding_fixed(self, file_path: str, pdf_info: Dict[str, Any]) -> str:
        """使用PyPDF2编码修复模式提取文本"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self._extract_with_pypdf2_encoding_fixed_sync, file_path
        )

    def _extract_with_pypdf2_encoding_fixed_sync(self, file_path: str) -> str:
        """PyPDF2编码修复模式（同步版本）"""
        text_content = []

        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        text = page.extract_text()
                        if text:
                            # 应用编码修复
                            fixed_text = self._fix_encoding_issues(text)
                            if fixed_text and len(fixed_text.strip()) > 10:
                                text_content.append(f"[第{page_num + 1}页]\n{fixed_text}")
                    except Exception as e:
                        logger.debug(f"PyPDF2编码修复页面 {page_num + 1} 失败: {e}")
                        continue
        except Exception as e:
            logger.error(f"PyPDF2编码修复失败: {e}")

        return '\n\n'.join(text_content)

    def _fix_encoding_issues(self, text: str) -> str:
        """修复编码问题"""
        if not text:
            return ""

        # 尝试修复常见的编码问题
        try:
            # 移除替换字符
            text = text.replace('\ufffd', '')

            # 尝试修复UTF-8编码问题
            if isinstance(text, str):
                # 检查是否有编码问题
                try:
                    text.encode('utf-8')
                except UnicodeEncodeError:
                    # 尝试用latin-1重新解码
                    text = text.encode('latin-1', errors='ignore').decode('utf-8', errors='ignore')

            # 清理和增强处理
            return self._clean_pdf_text_enhanced(text)

        except Exception as e:
            logger.debug(f"编码修复失败: {e}")
            return self._clean_pdf_text_enhanced(text)

    def _assess_text_quality_enhanced(self, text: str) -> float:
        """增强的文本质量评估（0-1分）"""
        if not text or len(text) < 10:
            return 0.0

        # 计算各种字符的比例
        total_chars = len(text)
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        digits = len(re.findall(r'\d', text))
        spaces = len(re.findall(r'\s', text))
        punctuation = len(re.findall(r'[.,;:!?()[\]{}"\'-]', text))

        # 计算可读字符比例
        readable_chars = chinese_chars + english_chars + digits + spaces + punctuation
        readable_ratio = readable_chars / total_chars if total_chars > 0 else 0

        # 计算特殊字符比例
        special_chars = total_chars - readable_chars
        special_ratio = special_chars / total_chars if total_chars > 0 else 0

        # 基础质量评分
        quality_score = readable_ratio - (special_ratio * 2.5)  # 增加特殊字符惩罚

        # 内容质量加分
        if chinese_chars > 0:  # 包含中文
            quality_score += 0.15
        if english_chars > 0:  # 包含英文
            quality_score += 0.1
        if len(text) > 100:  # 内容充实
            quality_score += 0.1
        if len(text) > 500:  # 内容丰富
            quality_score += 0.05

        # 结构质量检查
        lines = text.split('\n')
        if len(lines) > 3:  # 多行内容
            quality_score += 0.05

        # 检查是否有完整的句子
        sentences = re.split(r'[.!?。！？]', text)
        complete_sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        if len(complete_sentences) > 2:
            quality_score += 0.1

        return max(0.0, min(1.0, quality_score))

    def _assess_text_quality(self, text: str) -> float:
        """评估文本质量（0-1分）"""
        if not text or len(text) < 10:
            return 0.0

        import re

        # 计算各种字符的比例
        total_chars = len(text)
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        digits = len(re.findall(r'\d', text))
        spaces = len(re.findall(r'\s', text))
        punctuation = len(re.findall(r'[.,;:!?()[\]{}"\'-]', text))

        # 计算可读字符比例
        readable_chars = chinese_chars + english_chars + digits + spaces + punctuation
        readable_ratio = readable_chars / total_chars if total_chars > 0 else 0

        # 计算特殊字符比例
        special_chars = total_chars - readable_chars
        special_ratio = special_chars / total_chars if total_chars > 0 else 0

        # 质量评分
        quality_score = readable_ratio - (special_ratio * 2)  # 特殊字符惩罚更重

        # 额外加分项
        if chinese_chars > 0:  # 包含中文
            quality_score += 0.1
        if english_chars > 0:  # 包含英文
            quality_score += 0.1
        if len(text) > 100:  # 内容充实
            quality_score += 0.1

        return max(0.0, min(1.0, quality_score))

    def _clean_pdf_text_enhanced(self, text: str) -> str:
        """增强的PDF文本清理"""
        if not text:
            return ""

        # 预处理：移除控制字符
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t\r')

        # 修复常见的PDF提取问题
        text = text.replace('\x00', '')  # 移除空字符
        text = text.replace('\ufffd', '')  # 移除替换字符
        text = re.sub(r'\s+', ' ', text)  # 规范化空白字符

        # 移除页眉页脚模式
        text = re.sub(r'^\d+\s*$', '', text, flags=re.MULTILINE)  # 单独的页码
        text = re.sub(r'^第\s*\d+\s*页\s*$', '', text, flags=re.MULTILINE)  # 页码标识
        text = re.sub(r'^Page\s+\d+\s*$', '', text, flags=re.MULTILINE)  # 英文页码

        # 修复断行问题
        text = self._fix_line_breaks(text)

        # 移除过短的行和可能的乱码行
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if len(line) > 2:  # 保留长度大于2的行
                # 检查行的质量
                if self._is_valid_line(line):
                    cleaned_lines.append(line)

        result = '\n'.join(cleaned_lines).strip()

        # 最终质量检查
        if len(result) < 10:
            return ""

        # 检查特殊字符比例
        special_char_ratio = self._calculate_special_char_ratio(result)
        if special_char_ratio > 0.6:  # 如果特殊字符超过60%，可能是乱码
            logger.warning(f"检测到可能的乱码文本，特殊字符比例: {special_char_ratio:.2%}")
            return ""

        return result

    def _fix_line_breaks(self, text: str) -> str:
        """修复PDF中的断行问题"""
        # 修复中文断行
        text = re.sub(r'([\u4e00-\u9fff])\s*\n\s*([\u4e00-\u9fff])', r'\1\2', text)

        # 修复英文单词断行
        text = re.sub(r'([a-z])-\s*\n\s*([a-z])', r'\1\2', text)

        # 修复句子断行
        text = re.sub(r'([.!?。！？])\s*\n\s*([A-Z\u4e00-\u9fff])', r'\1 \2', text)

        return text

    def _is_valid_line(self, line: str) -> bool:
        """检查行是否有效"""
        if not line:
            return False

        # 检查是否主要由特殊字符组成
        special_chars = sum(1 for c in line if not c.isalnum() and not c.isspace() and not '\u4e00' <= c <= '\u9fff')
        if len(line) > 0 and special_chars / len(line) > 0.7:
            return False

        # 检查是否包含有意义的内容
        meaningful_chars = sum(1 for c in line if c.isalnum() or '\u4e00' <= c <= '\u9fff')
        if meaningful_chars < 2:
            return False

        return True

    def _calculate_special_char_ratio(self, text: str) -> float:
        """计算特殊字符比例"""
        if not text:
            return 0.0

        special_chars = len(re.findall(r'[^\w\s\u4e00-\u9fff.,;:!?()[\]{}"\'-]', text))
        return special_chars / len(text) if text else 0.0

    def _clean_pdf_text(self, text: str) -> str:
        """清理PDF提取的文本"""
        if not text:
            return ""

        import re

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除页眉页脚中的常见模式
        text = re.sub(r'^\d+\s*$', '', text, flags=re.MULTILINE)  # 单独的页码
        text = re.sub(r'^第\s*\d+\s*页\s*$', '', text, flags=re.MULTILINE)  # 页码标识

        # 修复常见的PDF提取问题
        text = text.replace('', ' ')  # 替换特殊空格
        text = text.replace('\x00', '')  # 移除空字符

        # 移除过短的行（可能是乱码）
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if len(line) > 2:  # 保留长度大于2的行
                cleaned_lines.append(line)

        result = '\n'.join(cleaned_lines).strip()

        # 如果结果太短或包含太多特殊字符，可能是乱码
        if len(result) < 10:
            return ""

        # 检查是否包含过多的特殊字符（可能是乱码）
        special_char_ratio = len(re.findall(r'[^\w\s\u4e00-\u9fff]', result)) / len(result) if result else 0
        if special_char_ratio > 0.5:  # 如果特殊字符超过50%，可能是乱码
            print(f"警告: 检测到可能的乱码文本，特殊字符比例: {special_char_ratio:.2%}")

        return result

    def _extract_table_text_enhanced(self, tables: list) -> str:
        """增强的表格文本提取"""
        if not tables:
            return ""

        table_texts = []

        for table_idx, table in enumerate(tables):
            if not table or not any(table):
                continue

            table_content = []
            header_row = None

            for row_idx, row in enumerate(table):
                if not row or not any(cell for cell in row if cell is not None):
                    continue

                # 清理和格式化单元格内容
                cleaned_row = []
                for cell in row:
                    if cell is not None:
                        cell_text = str(cell).strip()
                        # 清理单元格文本
                        cell_text = re.sub(r'\s+', ' ', cell_text)
                        cell_text = cell_text.replace('\n', ' ')
                        cleaned_row.append(cell_text)
                    else:
                        cleaned_row.append("")

                # 检查是否为有效行
                if any(cell for cell in cleaned_row):
                    if row_idx == 0 and self._is_likely_header(cleaned_row):
                        header_row = cleaned_row
                        table_content.append("| " + " | ".join(cleaned_row) + " |")
                        # 添加分隔线
                        separator = "| " + " | ".join(["---"] * len(cleaned_row)) + " |"
                        table_content.append(separator)
                    else:
                        table_content.append("| " + " | ".join(cleaned_row) + " |")

            if table_content:
                table_title = f"表格 {table_idx + 1}"
                if header_row:
                    table_title += f" ({', '.join(header_row[:3])}...)" if len(header_row) > 3 else f" ({', '.join(header_row)})"

                table_text = f"{table_title}:\n" + "\n".join(table_content)
                table_texts.append(table_text)

        return "\n\n".join(table_texts)

    def _is_likely_header(self, row: list) -> bool:
        """判断是否为表格标题行"""
        if not row:
            return False

        # 检查是否包含典型的标题词汇
        header_indicators = ['名称', '类型', '数量', '价格', '日期', '编号', 'name', 'type', 'count', 'price', 'date', 'id']
        row_text = ' '.join(str(cell).lower() for cell in row if cell)

        return any(indicator in row_text for indicator in header_indicators)

    def _extract_table_text(self, tables: list) -> str:
        """从表格中提取文本"""
        table_texts = []

        for table_idx, table in enumerate(tables):
            if not table:
                continue

            table_content = []
            for row in table:
                if row:
                    # 过滤掉None值并转换为字符串
                    row_text = [str(cell).strip() if cell is not None else "" for cell in row]
                    # 只保留非空行
                    if any(cell for cell in row_text):
                        table_content.append(" | ".join(row_text))

            if table_content:
                table_text = f"表格 {table_idx + 1}:\n" + "\n".join(table_content)
                table_texts.append(table_text)

        return "\n\n".join(table_texts)

    async def _extract_from_docx(self, file_path: str) -> str:
        """从DOCX文件提取文本"""
        doc = Document(file_path)
        text_content = []
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_content.append(paragraph.text)
        
        return '\n\n'.join(text_content)
    
    async def delete_file(self, file_id: str, file_type: FileType) -> bool:
        """删除文件"""
        try:
            file_path = self._get_file_path(file_id, file_type)
            if os.path.exists(file_path):
                os.remove(file_path)
            return True
        except Exception:
            return False
    
    def get_content_preview(self, content: str, max_length: int = 200) -> str:
        """获取内容预览"""
        if len(content) <= max_length:
            return content
        return content[:max_length] + "..."
