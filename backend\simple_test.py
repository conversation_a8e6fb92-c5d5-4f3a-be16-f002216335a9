#!/usr/bin/env python3
"""
简单的API测试
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_basic_endpoints():
    """测试基本端点"""
    print("🔍 测试基本端点...")
    
    # 测试健康检查
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"健康检查: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"健康检查失败: {e}")
    
    # 测试系统状态
    try:
        response = requests.get(f"{BASE_URL}/status")
        print(f"系统状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  总文档数: {data.get('total_documents', 0)}")
            print(f"  向量存储: {data.get('vector_store_type', 'Unknown')}")
    except Exception as e:
        print(f"系统状态失败: {e}")
    
    # 测试文档列表
    try:
        response = requests.get(f"{BASE_URL}/documents")
        print(f"文档列表: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  文档数量: {len(data.get('documents', []))}")
            for doc in data.get('documents', [])[:3]:  # 只显示前3个
                print(f"    - {doc.get('filename')} (ID: {doc.get('id')[:8]}...)")
    except Exception as e:
        print(f"文档列表失败: {e}")

def test_upload():
    """测试文档上传"""
    print("\n🔍 测试文档上传...")
    
    try:
        with open("test_document.txt", "rb") as f:
            files = {"file": ("test_document.txt", f, "text/plain")}
            response = requests.post(f"{BASE_URL}/upload", files=files)
            
        print(f"上传状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 上传成功: {data.get('filename')}")
            print(f"  文档ID: {data.get('document_id')}")
            return data.get('document_id')
        else:
            print(f"❌ 上传失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return None

def main():
    """主函数"""
    print("🚀 开始简单API测试...")
    
    # 测试基本端点
    test_basic_endpoints()
    
    # 测试上传
    doc_id = test_upload()
    
    print("\n🎉 简单测试完成!")

if __name__ == "__main__":
    main()
