# 知识库系统安装和启动指南

## 系统要求

### 软件要求
- **Python 3.8+** (推荐 3.9 或 3.10)
- **Node.js 16+** (推荐 18 或更高版本)
- **npm** 或 **yarn**

### API要求
- **OpenAI API Key** (用于文本嵌入和LLM功能)

## 快速启动

### 1. 克隆或下载项目
确保您已经获得了完整的项目文件。

### 2. 后端设置

#### 2.1 进入后端目录
```bash
cd backend
```

#### 2.2 创建虚拟环境
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

#### 2.3 安装依赖
```bash
pip install -r requirements.txt
```

#### 2.4 配置环境变量
1. 复制 `.env.example` 为 `.env`
2. 编辑 `.env` 文件，设置您的 OpenAI API Key：
```
OPENAI_API_KEY=your_actual_openai_api_key_here
```

#### 2.5 测试后端
```bash
python test_startup.py
```

#### 2.6 启动后端服务
```bash
# 方式1: 使用启动脚本 (Windows)
start_backend.bat

# 方式2: 直接命令
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

后端服务将在 `http://localhost:8000` 启动
API文档: `http://localhost:8000/docs`

### 3. 前端设置

#### 3.1 进入前端目录
```bash
cd frontend
```

#### 3.2 安装依赖
```bash
npm install
```

#### 3.3 启动前端服务
```bash
# 方式1: 使用启动脚本 (Windows)
start_frontend.bat

# 方式2: 直接命令
npm run dev
```

前端服务将在 `http://localhost:5173` 启动

## 使用指南

### 1. 访问系统
打开浏览器，访问 `http://localhost:5173`

### 2. 上传文档
- 点击"文档上传"菜单
- 拖拽或点击上传支持的文件格式：
  - TXT 文本文件
  - Markdown 文件 (.md, .markdown)
  - PDF 文档
  - Word 文档 (.docx, .doc)

### 3. 搜索文档
- 点击"智能搜索"菜单
- 输入自然语言查询
- 调整返回结果数量
- 查看搜索结果和相似度分数

### 4. 管理文档
- 点击"文档管理"菜单
- 查看所有已上传的文档
- 查看文档状态和详情
- 删除不需要的文档

## 故障排除

### 常见问题

#### 1. 后端启动失败
- 检查 Python 版本是否为 3.8+
- 确保已安装所有依赖: `pip install -r requirements.txt`
- 检查 OpenAI API Key 是否正确设置
- 查看错误日志，通常是依赖包问题

#### 2. 前端启动失败
- 检查 Node.js 版本是否为 16+
- 删除 `node_modules` 文件夹，重新运行 `npm install`
- 检查端口 5173 是否被占用

#### 3. 文档上传失败
- 检查文件格式是否支持
- 确保文件大小不超过 50MB
- 检查后端服务是否正常运行

#### 4. 搜索无结果
- 确保已上传并索引了文档
- 检查文档处理状态是否为"已索引"
- 尝试使用不同的搜索关键词

### 日志查看
- 后端日志：在后端启动终端中查看
- 前端日志：在浏览器开发者工具的控制台中查看

## 高级配置

### 自定义配置
编辑 `backend/.env` 文件可以修改：
- 文件上传大小限制
- 文本分块大小
- 搜索结果数量
- 服务器端口等

### 使用本地LLM
如果您想使用本地LLM而不是OpenAI API：
1. 安装 Ollama
2. 在 `.env` 中设置 `USE_LOCAL_LLM=True`
3. 配置本地模型名称

## 技术支持

如果遇到问题：
1. 查看本指南的故障排除部分
2. 检查项目的 README.md 文件
3. 查看 GitHub Issues (如果项目在GitHub上)

## 系统架构

```
知识库系统
├── 前端 (React + Vite)
│   ├── 文件上传界面
│   ├── 智能搜索界面
│   └── 文档管理界面
├── 后端 (FastAPI + LlamaIndex)
│   ├── 文档处理服务
│   ├── 向量索引服务
│   └── 搜索检索服务
└── 存储
    ├── 文件存储 (./data)
    └── 向量数据库 (./vector_store)
```
