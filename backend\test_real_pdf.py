#!/usr/bin/env python3
"""
测试真实PDF文档的解析改进
"""
import requests
import json
import os

BASE_URL = "http://localhost:8000/api/v1"

def find_pdf_files():
    """查找可用的PDF文件"""
    pdf_files = []

    # 检查data目录（实际存储目录）
    data_dir = "data"
    if os.path.exists(data_dir):
        for file in os.listdir(data_dir):
            if file.endswith('.pdf'):
                pdf_files.append(os.path.join(data_dir, file))

    # 检查uploads目录
    upload_dir = "uploads"
    if os.path.exists(upload_dir):
        for file in os.listdir(upload_dir):
            if file.endswith('.pdf'):
                pdf_files.append(os.path.join(upload_dir, file))

    # 检查当前目录
    for file in os.listdir('.'):
        if file.endswith('.pdf'):
            pdf_files.append(file)

    return pdf_files

def upload_pdf_file(file_path):
    """上传PDF文件"""
    print(f"📄 上传PDF文件: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/pdf')
            }
            
            response = requests.post(f"{BASE_URL}/upload", files=files, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ PDF上传成功: {data.get('filename')}")
                print(f"  文档ID: {data.get('document_id')}")
                return data.get('document_id')
            else:
                print(f"❌ PDF上传失败: {response.status_code} - {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return None

def check_pdf_processing(doc_id, max_wait=30):
    """检查PDF处理状态"""
    print(f"\n🔍 检查PDF处理状态: {doc_id}")
    
    import time
    wait_time = 0
    
    while wait_time < max_wait:
        try:
            response = requests.get(f"{BASE_URL}/documents", timeout=10)
            if response.status_code == 200:
                data = response.json()
                documents = data.get('documents', [])
                
                # 查找我们的文档
                for doc in documents:
                    if doc.get('id') == doc_id:
                        status = doc.get('status')
                        print(f"📄 文档状态: {status}")
                        
                        if status == 'indexed':
                            print("✅ PDF处理完成")
                            return doc
                        elif status == 'error':
                            print(f"❌ PDF处理失败: {doc.get('error_message', '未知错误')}")
                            return doc
                        elif status == 'processing':
                            print("⏳ PDF正在处理中...")
                            time.sleep(2)
                            wait_time += 2
                            continue
                        else:
                            print(f"⚠️ 未知状态: {status}")
                            return doc
                
                print(f"❌ 未找到文档 {doc_id}")
                return None
                
        except Exception as e:
            print(f"❌ 检查状态失败: {e}")
            return None
    
    print(f"⏰ 等待超时 ({max_wait}秒)")
    return None

def analyze_pdf_content(doc_info):
    """分析PDF内容质量"""
    print(f"\n📊 分析PDF内容质量...")
    
    if not doc_info:
        print("❌ 无文档信息")
        return False
    
    filename = doc_info.get('filename', '')
    file_size = doc_info.get('file_size', 0)
    chunk_count = doc_info.get('chunk_count', 0)
    preview = doc_info.get('content_preview', '')
    
    print(f"📄 文件名: {filename}")
    print(f"📏 文件大小: {file_size} 字节")
    print(f"🧩 块数量: {chunk_count}")
    
    if preview:
        print(f"📝 内容预览长度: {len(preview)} 字符")
        print(f"📝 内容预览:\n{preview}")
        
        # 分析文本质量
        chinese_chars = sum(1 for char in preview if '\u4e00' <= char <= '\u9fff')
        english_chars = sum(1 for char in preview if char.isalpha() and ord(char) < 128)
        special_chars = sum(1 for char in preview if not char.isalnum() and not char.isspace() and not '\u4e00' <= char <= '\u9fff')
        
        print(f"📊 文本分析:")
        print(f"   中文字符: {chinese_chars}")
        print(f"   英文字符: {english_chars}")
        print(f"   特殊字符: {special_chars}")
        
        # 检查乱码
        total_chars = len(preview)
        if total_chars > 0:
            special_ratio = special_chars / total_chars
            if special_ratio > 0.3:
                print(f"⚠️ 可能包含乱码 (特殊字符比例: {special_ratio:.1%})")
                return False
            else:
                print(f"✅ 文本质量良好 (特殊字符比例: {special_ratio:.1%})")
                return True
        else:
            print("❌ 无有效内容")
            return False
    else:
        print("❌ 无内容预览")
        return False

def test_pdf_search(doc_id, filename):
    """测试PDF搜索功能"""
    print(f"\n🔍 测试PDF搜索功能...")
    
    # 根据文件名生成搜索词
    search_queries = []
    
    if "结直肠癌" in filename:
        search_queries.extend(["结直肠癌", "Meta分析", "疾病", "转归", "参数"])
    elif "大肠癌" in filename:
        search_queries.extend(["大肠癌", "临床", "病理", "分析"])
    else:
        search_queries.extend(["文档", "内容", "分析", "研究", "方法"])
    
    for query in search_queries:
        try:
            print(f"\n🔎 搜索: '{query}'")
            
            response = requests.post(f"{BASE_URL}/search", 
                json={"query": query, "top_k": 3}, 
                timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                print(f"   📊 找到 {len(results)} 个结果")
                
                found_our_doc = False
                for i, result in enumerate(results):
                    content = result.get('content', '')
                    score = result.get('score', 0)
                    doc_name = result.get('document_name', '')
                    
                    print(f"   结果 {i+1}: {doc_name} (相似度: {score:.3f})")
                    print(f"   内容: {content[:100]}...")
                    
                    # 检查是否是我们的文档
                    if result.get('document_id') == doc_id:
                        found_our_doc = True
                        print("   ✅ 找到我们上传的PDF文档")
                
                if not found_our_doc and len(results) > 0:
                    print("   ⚠️ 未找到我们的PDF文档")
                    
            else:
                print(f"   ❌ 搜索失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 搜索异常: {e}")

def main():
    """主测试函数"""
    print("🚀 开始真实PDF解析测试...")
    print("=" * 60)
    
    # 1. 查找PDF文件
    pdf_files = find_pdf_files()
    
    if not pdf_files:
        print("❌ 未找到PDF文件进行测试")
        print("请将PDF文件放在uploads目录或当前目录中")
        return
    
    print(f"📄 找到 {len(pdf_files)} 个PDF文件:")
    for i, file in enumerate(pdf_files):
        print(f"  {i+1}. {file}")
    
    # 2. 测试第一个PDF文件
    test_file = pdf_files[0]
    print(f"\n🧪 测试文件: {test_file}")
    
    # 3. 上传PDF
    doc_id = upload_pdf_file(test_file)
    
    if doc_id:
        # 4. 等待处理完成
        doc_info = check_pdf_processing(doc_id, max_wait=60)
        
        if doc_info:
            # 5. 分析内容质量
            content_ok = analyze_pdf_content(doc_info)
            
            if content_ok:
                # 6. 测试搜索功能
                test_pdf_search(doc_id, os.path.basename(test_file))
                print("\n✅ PDF解析改进测试成功!")
            else:
                print("\n⚠️ PDF内容质量有问题，可能需要进一步优化")
        else:
            print("\n❌ PDF处理失败")
    else:
        print("\n❌ PDF上传失败")
    
    print("=" * 60)
    print("🎉 真实PDF解析测试完成!")

if __name__ == "__main__":
    main()
