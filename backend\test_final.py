#!/usr/bin/env python3
"""
最终测试 - 验证系统基本功能
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_system_status():
    """测试系统状态"""
    print("🔍 测试系统状态...")
    
    try:
        # 健康检查
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print(f"✅ 健康检查: {response.status_code}")
        
        # 系统状态
        response = requests.get(f"{BASE_URL}/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 系统状态: {response.status_code}")
            print(f"  总文档数: {data.get('total_documents', 0)}")
            print(f"  向量存储: {data.get('vector_store_type', 'Unknown')}")
            print(f"  嵌入模型: {data.get('embedding_model', 'Unknown')}")
        
        # 文档列表
        response = requests.get(f"{BASE_URL}/documents", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 文档列表: {response.status_code}")
            print(f"  文档数量: {len(data.get('documents', []))}")
            
            # 显示文档详情
            for doc in data.get('documents', []):
                print(f"    - {doc.get('filename')} (状态: {doc.get('status', 'Unknown')})")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统状态测试失败: {e}")
        return False

def test_document_upload():
    """测试文档上传（不依赖搜索）"""
    print("\n🔍 测试文档上传...")
    
    try:
        # 创建一个简单的测试文档
        test_content = "这是一个测试文档。包含一些中文内容用于测试知识库系统的基本功能。"
        
        # 模拟文件上传
        files = {
            'file': ('test_simple.txt', test_content.encode('utf-8'), 'text/plain')
        }
        
        response = requests.post(f"{BASE_URL}/upload", files=files, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 文档上传成功: {data.get('filename')}")
            print(f"  文档ID: {data.get('document_id')}")
            return data.get('document_id')
        else:
            print(f"❌ 文档上传失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 文档上传异常: {e}")
        return None

def main():
    """主测试函数"""
    print("🚀 开始最终系统测试...")
    print("=" * 50)
    
    # 测试系统状态
    status_ok = test_system_status()
    
    if status_ok:
        # 测试文档上传
        doc_id = test_document_upload()
        
        if doc_id:
            print(f"\n✅ 基本功能测试通过!")
            print("📋 测试总结:")
            print("  ✅ 后端服务正常运行")
            print("  ✅ API端点响应正常")
            print("  ✅ 文档上传功能正常")
            print("  ✅ 文档管理功能正常")
            print("\n⚠️  注意事项:")
            print("  - 搜索功能可能因为嵌入模型问题而受限")
            print("  - 建议配置有效的OpenAI API密钥或安装本地嵌入模型")
        else:
            print(f"\n❌ 文档上传功能异常")
    else:
        print(f"\n❌ 系统状态异常")
    
    print("=" * 50)
    print("🎉 最终测试完成!")

if __name__ == "__main__":
    main()
