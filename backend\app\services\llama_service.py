"""
LlamaIndex集成服务
"""
import os
import json
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime

# LlamaIndex核心组件
from llama_index.core import (
    VectorStoreIndex,
    Document,
    Settings,
    StorageContext
)
from llama_index.core.node_parser import SentenceSplitter
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI

# 尝试导入HuggingFace嵌入模型
try:
    from llama_index.embeddings.huggingface import HuggingFaceEmbedding
    HUGGINGFACE_AVAILABLE = False  # 暂时禁用以避免网络下载问题
except ImportError:
    HUGGINGFACE_AVAILABLE = False

# 导入简单嵌入模型
from app.services.simple_embedding import create_simple_embedding_model

# 尝试导入ChromaVectorStore，如果失败则使用简单向量存储
try:
    from llama_index.vector_stores.chroma import ChromaVectorStore
    CHROMA_AVAILABLE = True
except ImportError:
    from llama_index.core.vector_stores import SimpleVectorStore as ChromaVectorStore
    CHROMA_AVAILABLE = False
    print("警告: ChromaVectorStore不可用，使用SimpleVectorStore")

# Chroma向量数据库
import chromadb
from chromadb.config import Settings as ChromaSettings

from app.core.config import get_settings
from app.models.schemas import DocumentInfo, SearchResult, SearchResponse

settings = get_settings()


class LlamaIndexService:
    """LlamaIndex服务类"""
    
    def __init__(self):
        self.settings = settings
        self.index: Optional[VectorStoreIndex] = None
        self.chroma_client = None
        self.collection = None
        self._setup_llama_index()
        self._initialize_vector_store()
    
    def _setup_llama_index(self):
        """配置LlamaIndex全局设置"""
        # 配置LLM
        if self.settings.use_local_llm:
            # 这里可以配置本地LLM，如Ollama
            pass
        else:
            Settings.llm = OpenAI(
                api_key=self.settings.openai_api_key,
                base_url=self.settings.openai_api_base_url,
                model="gpt-3.5-turbo",
                temperature=0.1,
                timeout=60.0,  # 增加超时时间
                max_retries=3
            )

        # 配置嵌入模型
        embedding_configured = False
        force_local = getattr(self.settings, 'force_local_embedding', False)

        # 如果强制使用本地嵌入或者启用本地嵌入且HuggingFace可用
        if (force_local or self.settings.use_local_embedding) and HUGGINGFACE_AVAILABLE:
            try:
                Settings.embed_model = HuggingFaceEmbedding(
                    model_name=self.settings.local_embedding_model
                )
                print(f"✅ 使用本地HuggingFace嵌入模型: {self.settings.local_embedding_model}")
                embedding_configured = True
            except Exception as e:
                print(f"⚠️ 本地嵌入模型配置失败: {e}")

        # 如果不强制使用本地模型且本地嵌入未配置成功，尝试OpenAI
        if not force_local and not embedding_configured and self.settings.openai_api_key:
            try:
                Settings.embed_model = OpenAIEmbedding(
                    api_key=self.settings.openai_api_key,
                    base_url=self.settings.openai_api_base_url,
                    model="text-embedding-ada-002",
                    timeout=10.0,  # 减少超时时间
                    max_retries=1  # 减少重试次数以快速失败
                )
                print("✅ 使用OpenAI嵌入模型")
                embedding_configured = True
            except Exception as e:
                print(f"⚠️ OpenAI嵌入模型配置失败: {e}")

        # 如果都失败了，使用简单嵌入模型作为最后备用方案
        if not embedding_configured:
            try:
                simple_model = create_simple_embedding_model()
                Settings.embed_model = simple_model
                print("✅ 使用简单嵌入模型（基本功能）")
                embedding_configured = True
            except Exception as e:
                print(f"❌ 简单嵌入模型也失败: {str(e)}")
                import traceback
                traceback.print_exc()
                print("⚠️ 使用默认嵌入模型（功能受限）")
                # LlamaIndex会使用默认的嵌入模型
        
        # 配置文本分割器
        Settings.node_parser = SentenceSplitter(
            chunk_size=self.settings.chunk_size,
            chunk_overlap=self.settings.chunk_overlap
        )
    
    def _initialize_vector_store(self):
        """初始化向量存储"""
        try:
            if CHROMA_AVAILABLE:
                # 使用Chroma向量存储
                self.chroma_client = chromadb.PersistentClient(
                    path=self.settings.vector_store_path,
                    settings=ChromaSettings(anonymized_telemetry=False)
                )

                # 获取或创建集合
                self.collection = self.chroma_client.get_or_create_collection(
                    name="knowledge_base"
                )

                # 创建ChromaVectorStore
                vector_store = ChromaVectorStore(chroma_collection=self.collection)
                storage_context = StorageContext.from_defaults(vector_store=vector_store)

                # 尝试加载现有索引
                try:
                    self.index = VectorStoreIndex.from_vector_store(
                        vector_store=vector_store
                    )
                    print("成功加载现有Chroma向量索引")
                except Exception:
                    # 如果没有现有索引，创建新的空索引
                    self.index = VectorStoreIndex(
                        nodes=[],
                        storage_context=storage_context
                    )
                    print("创建新的Chroma向量索引")
            else:
                # 使用简单向量存储作为后备
                print("使用简单向量存储")
                self.index = VectorStoreIndex([])
                self.chroma_client = None
                self.collection = None

        except Exception as e:
            print(f"初始化向量存储失败: {e}")
            # 作为最后的后备，使用简单向量存储
            print("使用简单向量存储作为后备")
            self.index = VectorStoreIndex([])
            self.chroma_client = None
            self.collection = None
    
    async def add_document(self, document_info: DocumentInfo, content: str) -> bool:
        """添加文档到索引"""
        try:
            # 创建LlamaIndex文档对象
            document = Document(
                text=content,
                metadata={
                    "document_id": document_info.id,
                    "filename": document_info.filename,
                    "file_type": document_info.file_type.value,
                    "upload_time": document_info.upload_time.isoformat(),
                    "file_size": document_info.file_size
                }
            )
            
            # 将文档添加到索引
            self.index.insert(document)
            
            # 保存索引（Chroma会自动持久化）
            print(f"文档 {document_info.filename} 已添加到索引")
            return True
            
        except Exception as e:
            print(f"添加文档到索引失败: {e}")
            return False
    
    async def search_documents(
        self, 
        query: str, 
        top_k: int = 5,
        document_ids: Optional[List[str]] = None
    ) -> SearchResponse:
        """搜索文档"""
        start_time = datetime.now()
        
        try:
            # 创建查询引擎
            query_engine = self.index.as_query_engine(
                similarity_top_k=top_k,
                response_mode="no_text"  # 只返回检索结果，不生成回答
            )
            
            # 执行查询
            response = query_engine.query(query)
            
            # 处理搜索结果
            results = []
            for i, node in enumerate(response.source_nodes):
                # 检查是否需要过滤特定文档
                if document_ids:
                    doc_id = node.metadata.get("document_id")
                    if doc_id not in document_ids:
                        continue
                
                result = SearchResult(
                    content=node.text,
                    score=node.score if hasattr(node, 'score') else 0.0,
                    document_id=node.metadata.get("document_id", ""),
                    document_name=node.metadata.get("filename", ""),
                    chunk_index=i,
                    metadata=node.metadata
                )
                results.append(result)
            
            # 计算搜索时间
            search_time = (datetime.now() - start_time).total_seconds()
            
            return SearchResponse(
                success=True,
                query=query,
                results=results,
                total_results=len(results),
                search_time=search_time
            )
            
        except Exception as e:
            search_time = (datetime.now() - start_time).total_seconds()
            print(f"搜索失败: {e}")
            
            return SearchResponse(
                success=False,
                query=query,
                results=[],
                total_results=0,
                search_time=search_time
            )
    
    async def delete_document(self, document_id: str) -> bool:
        """从索引中删除文档"""
        try:
            # 注意：LlamaIndex的删除功能可能需要重建索引
            # 这里提供一个简化的实现
            # 在生产环境中，可能需要更复杂的删除逻辑
            
            # 获取所有文档节点
            all_nodes = self.index.docstore.docs
            
            # 过滤掉要删除的文档
            remaining_nodes = []
            for node_id, node in all_nodes.items():
                if node.metadata.get("document_id") != document_id:
                    remaining_nodes.append(node)
            
            # 重建索引（简化实现）
            if remaining_nodes:
                # 创建新的向量存储
                vector_store = ChromaVectorStore(chroma_collection=self.collection)
                storage_context = StorageContext.from_defaults(vector_store=vector_store)
                
                # 重建索引
                self.index = VectorStoreIndex(
                    nodes=remaining_nodes,
                    storage_context=storage_context
                )
            
            print(f"文档 {document_id} 已从索引中删除")
            return True
            
        except Exception as e:
            print(f"删除文档失败: {e}")
            return False
    
    def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        try:
            if self.collection and CHROMA_AVAILABLE:
                # 获取Chroma集合信息
                collection_count = self.collection.count()
            else:
                # 简单向量存储的统计
                collection_count = len(self.index.docstore.docs) if self.index and hasattr(self.index, 'docstore') else 0

            return {
                "total_documents": collection_count,
                "vector_store_path": self.settings.vector_store_path,
                "chunk_size": self.settings.chunk_size,
                "chunk_overlap": self.settings.chunk_overlap,
                "vector_store_type": "Chroma" if CHROMA_AVAILABLE else "Simple"
            }
        except Exception as e:
            print(f"获取索引统计失败: {e}")
            return {
                "total_documents": 0,
                "vector_store_type": "Unknown",
                "error": str(e)
            }
