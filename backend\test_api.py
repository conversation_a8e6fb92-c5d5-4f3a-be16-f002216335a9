#!/usr/bin/env python3
"""
测试OpenAI API连接
"""
import os
import asyncio
import httpx
from openai import OpenAI

async def test_api_connection():
    """测试API连接"""
    api_base = "https://free.v36.cm/v1"
    api_key = "sk-3qdhHUo7FTRS69kC47D78131E69844B585EbD1307d4e31E8"
    
    print("🔍 测试API连接...")
    print(f"API Base URL: {api_base}")
    print(f"API Key: {api_key[:20]}...")
    
    try:
        # 测试基础连接
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(f"{api_base}/models", 
                                      headers={"Authorization": f"Bearer {api_key}"})
            print(f"✅ 基础连接测试: {response.status_code}")
            if response.status_code == 200:
                models = response.json()
                model_list = models.get('data', [])
                print(f"📋 可用模型数量: {len(model_list)}")
                print("📋 可用模型列表:")
                for model in model_list:
                    print(f"  - {model.get('id', 'unknown')}")
            else:
                print(f"❌ 响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 基础连接失败: {e}")
    
    try:
        # 测试嵌入API
        print("\n🔍 测试嵌入API...")
        async with httpx.AsyncClient(timeout=30.0) as client_http:
            embed_response = await client_http.post(
                f"{api_base}/embeddings",
                headers={"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"},
                json={
                    "model": "text-embedding-ada-002",
                    "input": "这是一个测试文本"
                }
            )
            print(f"嵌入API状态码: {embed_response.status_code}")
            print(f"嵌入API响应: {embed_response.text[:200]}...")

    except Exception as e:
        print(f"❌ 嵌入API测试失败: {e}")

    try:
        # 测试聊天API
        print("\n🔍 测试聊天API...")
        async with httpx.AsyncClient(timeout=30.0) as client_http:
            chat_response = await client_http.post(
                f"{api_base}/chat/completions",
                headers={"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"},
                json={
                    "model": "gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": "你好"}],
                    "max_tokens": 50
                }
            )
            print(f"聊天API状态码: {chat_response.status_code}")
            print(f"聊天API响应: {chat_response.text[:200]}...")

    except Exception as e:
        print(f"❌ 聊天API测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_api_connection())
