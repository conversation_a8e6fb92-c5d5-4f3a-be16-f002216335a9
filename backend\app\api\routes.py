"""
API路由定义
"""
from typing import List, Optional
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse

from app.models.schemas import (
    UploadResponse, SearchRequest, SearchResponse,
    DocumentListResponse, DeleteResponse, SystemStatus,
    DocumentInfo, ErrorResponse
)
from app.services.knowledge_base_service import KnowledgeBaseService
from app.core.config import get_settings

# 创建路由器
router = APIRouter()
settings = get_settings()

# 创建知识库服务实例
kb_service = KnowledgeBaseService()


@router.post("/upload", response_model=UploadResponse)
async def upload_document(file: UploadFile = File(...)):
    """
    上传文档文件
    
    支持的文件格式：
    - TXT (.txt)
    - Markdown (.md, .markdown)  
    - PDF (.pdf)
    - Word (.docx, .doc)
    """
    try:
        # 读取文件内容
        file_content = await file.read()
        
        # 调用服务处理上传
        result = await kb_service.upload_document(file_content, file.filename)
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")


@router.post("/search", response_model=SearchResponse)
async def search_documents(search_request: SearchRequest):
    """
    搜索文档内容
    
    参数：
    - query: 搜索查询字符串
    - top_k: 返回结果数量 (1-20)
    - document_ids: 可选，指定搜索的文档ID列表
    """
    try:
        result = await kb_service.search_documents(search_request)
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/documents", response_model=DocumentListResponse)
async def get_documents():
    """
    获取所有文档列表
    """
    try:
        result = await kb_service.get_documents()
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")


@router.get("/documents/{document_id}", response_model=DocumentInfo)
async def get_document(document_id: str):
    """
    获取单个文档信息
    """
    try:
        document = await kb_service.get_document(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        return document
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档信息失败: {str(e)}")


@router.delete("/documents/{document_id}", response_model=DeleteResponse)
async def delete_document(document_id: str):
    """
    删除文档
    """
    try:
        result = await kb_service.delete_document(document_id)
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除文档失败: {str(e)}")


@router.get("/status", response_model=SystemStatus)
async def get_system_status():
    """
    获取系统状态信息
    """
    try:
        result = await kb_service.get_system_status()
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


@router.get("/health")
async def health_check():
    """
    健康检查接口
    """
    return {"status": "healthy", "message": "Knowledge Base System is running"}


# 注意：异常处理器应该在主应用中定义，而不是在路由器中
