#!/usr/bin/env python3
"""
测试文档上传和搜索功能
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1"

def test_upload_document():
    """测试文档上传"""
    print("🔍 测试文档上传...")
    
    # 准备文件
    with open("test_document.txt", "rb") as f:
        files = {"file": ("test_document.txt", f, "text/plain")}
        
        try:
            response = requests.post(f"{BASE_URL}/upload", files=files)
            print(f"上传状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 文档上传成功")
                print(f"文档ID: {result.get('document_id')}")
                print(f"文件名: {result.get('filename')}")
                return result.get('document_id')
            else:
                print(f"❌ 文档上传失败: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 上传请求失败: {e}")
            return None

def test_list_documents():
    """测试文档列表"""
    print("\n🔍 测试文档列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/documents")
        print(f"列表状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取文档列表成功")
            print(f"文档数量: {len(result.get('documents', []))}")
            for doc in result.get('documents', []):
                print(f"  - {doc.get('filename')} (ID: {doc.get('id')})")
            return result.get('documents', [])
        else:
            print(f"❌ 获取文档列表失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 列表请求失败: {e}")
        return []

def test_search_documents(query="知识库系统"):
    """测试文档搜索"""
    print(f"\n🔍 测试文档搜索: '{query}'...")
    
    try:
        data = {"query": query, "top_k": 3}
        response = requests.post(f"{BASE_URL}/search", json=data)
        print(f"搜索状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 文档搜索成功")
            print(f"搜索时间: {result.get('search_time', 0):.3f}秒")
            print(f"结果数量: {result.get('total_results', 0)}")
            
            for i, res in enumerate(result.get('results', []), 1):
                print(f"\n结果 {i}:")
                print(f"  文档: {res.get('document_name')}")
                print(f"  相关度: {res.get('score', 0):.3f}")
                print(f"  内容片段: {res.get('content', '')[:100]}...")
                
            return result
        else:
            print(f"❌ 文档搜索失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 搜索请求失败: {e}")
        return None

def test_system_stats():
    """测试系统统计"""
    print("\n🔍 测试系统统计...")
    
    try:
        response = requests.get(f"{BASE_URL}/status")
        print(f"统计状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取系统统计成功")
            print(f"总文档数: {result.get('total_documents', 0)}")
            print(f"向量存储类型: {result.get('vector_store_type', 'Unknown')}")
            print(f"存储路径: {result.get('vector_store_path', 'Unknown')}")
            return result
        else:
            print(f"❌ 获取系统统计失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 统计请求失败: {e}")
        return None

def main():
    """主测试函数"""
    print("🚀 开始测试知识库系统功能...")
    
    # 测试文档上传
    doc_id = test_upload_document()
    
    # 等待一下让索引构建完成
    if doc_id:
        print("\n⏳ 等待索引构建...")
        time.sleep(2)
    
    # 测试文档列表
    documents = test_list_documents()
    
    # 测试文档搜索
    test_search_documents("知识库系统")
    test_search_documents("技术栈")
    test_search_documents("文档上传")
    
    # 测试系统统计
    test_system_stats()
    
    print("\n🎉 功能测试完成!")

if __name__ == "__main__":
    main()
