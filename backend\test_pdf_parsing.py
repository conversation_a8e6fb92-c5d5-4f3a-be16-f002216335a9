#!/usr/bin/env python3
"""
测试PDF解析改进
"""
import requests
import json
import os

BASE_URL = "http://localhost:8000/api/v1"

def test_pdf_upload_and_parsing():
    """测试PDF上传和解析"""
    print("🔍 测试PDF文档上传和解析...")
    
    # 查找PDF文件
    pdf_files = []
    upload_dir = "uploads"
    if os.path.exists(upload_dir):
        for file in os.listdir(upload_dir):
            if file.endswith('.pdf'):
                pdf_files.append(os.path.join(upload_dir, file))
    
    if not pdf_files:
        print("❌ 未找到PDF文件进行测试")
        return False
    
    print(f"📄 找到 {len(pdf_files)} 个PDF文件")
    
    # 测试文档列表
    try:
        response = requests.get(f"{BASE_URL}/documents", timeout=10)
        if response.status_code == 200:
            data = response.json()
            documents = data.get('documents', [])
            
            print(f"📋 当前文档数量: {len(documents)}")
            
            # 查找PDF文档
            pdf_docs = [doc for doc in documents if doc.get('file_type') == 'pdf']
            print(f"📄 PDF文档数量: {len(pdf_docs)}")
            
            # 显示PDF文档详情
            for doc in pdf_docs:
                print(f"\n📄 PDF文档: {doc.get('filename')}")
                print(f"   状态: {doc.get('status')}")
                print(f"   文件大小: {doc.get('file_size', 0)} 字节")
                print(f"   块数量: {doc.get('chunk_count', 0)}")
                
                # 显示内容预览
                preview = doc.get('content_preview', '')
                if preview:
                    print(f"   内容预览: {preview[:200]}...")
                    
                    # 检查是否包含乱码
                    if any(ord(char) > 127 and char not in '，。！？；：""''（）【】' for char in preview):
                        print("   ⚠️ 可能包含乱码字符")
                    else:
                        print("   ✅ 文本看起来正常")
                else:
                    print("   ❌ 无内容预览")
                
                # 如果有错误信息，显示出来
                if doc.get('error_message'):
                    print(f"   ❌ 错误: {doc.get('error_message')}")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_pdf_search():
    """测试PDF文档搜索"""
    print("\n🔍 测试PDF文档搜索...")
    
    # 测试搜索查询
    search_queries = [
        "结直肠癌",
        "Meta分析", 
        "疾病",
        "参数",
        "转归"
    ]
    
    for query in search_queries:
        try:
            print(f"\n🔎 搜索: '{query}'")
            
            response = requests.post(f"{BASE_URL}/search", 
                json={"query": query, "top_k": 3}, 
                timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                print(f"   📊 找到 {len(results)} 个结果")
                
                for i, result in enumerate(results):
                    content = result.get('content', '')
                    score = result.get('score', 0)
                    doc_name = result.get('document_name', '')
                    
                    print(f"   结果 {i+1}: {doc_name} (相似度: {score:.3f})")
                    print(f"   内容: {content[:150]}...")
                    
                    # 检查内容质量
                    if len(content) > 10 and not any(ord(char) > 127 and char not in '，。！？；：""''（）【】' for char in content[:100]):
                        print("   ✅ 内容质量良好")
                    else:
                        print("   ⚠️ 内容可能有问题")
            else:
                print(f"   ❌ 搜索失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 搜索异常: {e}")

def main():
    """主测试函数"""
    print("🚀 开始PDF解析测试...")
    print("=" * 60)
    
    # 测试PDF解析
    parsing_ok = test_pdf_upload_and_parsing()
    
    if parsing_ok:
        # 测试搜索
        test_pdf_search()
    
    print("=" * 60)
    print("🎉 PDF解析测试完成!")

if __name__ == "__main__":
    main()
