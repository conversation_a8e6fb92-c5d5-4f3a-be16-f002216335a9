"""
简单的本地嵌入模型实现
当HuggingFace sentence-transformers不可用时的备用方案
"""
import hashlib
import numpy as np
from typing import List, Optional
import logging

try:
    from llama_index.core.embeddings import BaseEmbedding
    LLAMA_INDEX_AVAILABLE = True
except ImportError:
    # 如果LlamaIndex不可用，创建一个简单的基类
    class BaseEmbedding:
        pass
    LLAMA_INDEX_AVAILABLE = False

logger = logging.getLogger(__name__)

class SimpleEmbedding:
    """
    简单的嵌入模型实现
    使用文本哈希和简单的向量化方法
    """
    
    def __init__(self, embed_dim: int = 384):
        """
        初始化简单嵌入模型
        
        Args:
            embed_dim: 嵌入向量维度
        """
        self.embed_dim = embed_dim
        self.model_name = "simple-embedding"
        logger.info(f"初始化简单嵌入模型，维度: {embed_dim}")
    
    def get_text_embedding(self, text: str) -> List[float]:
        """
        获取文本的嵌入向量
        
        Args:
            text: 输入文本
            
        Returns:
            嵌入向量列表
        """
        if not text or not text.strip():
            return [0.0] * self.embed_dim
        
        # 使用文本哈希生成种子
        text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()
        seed = int(text_hash[:8], 16)
        
        # 使用numpy生成确定性的随机向量
        np.random.seed(seed)
        embedding = np.random.normal(0, 1, self.embed_dim)
        
        # 归一化向量
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm
        
        return embedding.tolist()
    
    def get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        批量获取文本嵌入向量
        
        Args:
            texts: 文本列表
            
        Returns:
            嵌入向量列表
        """
        return [self.get_text_embedding(text) for text in texts]
    
    def similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        计算两个嵌入向量的相似度
        
        Args:
            embedding1: 第一个嵌入向量
            embedding2: 第二个嵌入向量
            
        Returns:
            相似度分数 (0-1)
        """
        if len(embedding1) != len(embedding2):
            return 0.0
        
        # 计算余弦相似度
        dot_product = sum(a * b for a, b in zip(embedding1, embedding2))
        norm1 = sum(a * a for a in embedding1) ** 0.5
        norm2 = sum(b * b for b in embedding2) ** 0.5
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        similarity = dot_product / (norm1 * norm2)
        # 将相似度从 [-1, 1] 映射到 [0, 1]
        return (similarity + 1) / 2


class LlamaIndexSimpleEmbedding(BaseEmbedding):
    """
    LlamaIndex兼容的简单嵌入模型包装器
    """

    def __init__(self, embed_dim: int = 384):
        super().__init__()
        self._embed_dim = embed_dim
        self.model_name = "simple-embedding"

    def _get_simple_embedding(self, text: str) -> List[float]:
        """获取简单嵌入向量"""
        if not text or not text.strip():
            return [0.0] * self._embed_dim

        # 使用文本哈希生成种子
        text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()
        seed = int(text_hash[:8], 16)

        # 使用numpy生成确定性的随机向量
        np.random.seed(seed)
        embedding = np.random.normal(0, 1, self._embed_dim)

        # 归一化向量
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm

        return embedding.tolist()

    def get_text_embedding(self, text: str) -> List[float]:
        """LlamaIndex兼容的文本嵌入方法"""
        return self._get_simple_embedding(text)

    def get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """LlamaIndex兼容的批量文本嵌入方法"""
        return [self._get_simple_embedding(text) for text in texts]

    def get_query_embedding(self, query: str) -> List[float]:
        """LlamaIndex兼容的查询嵌入方法"""
        return self._get_simple_embedding(query)

    def _get_text_embedding(self, text: str) -> List[float]:
        """内部方法，某些LlamaIndex版本可能需要"""
        return self._get_simple_embedding(text)

    def _get_query_embedding(self, query: str) -> List[float]:
        """内部方法，某些LlamaIndex版本可能需要"""
        return self._get_simple_embedding(query)

    async def _aget_query_embedding(self, query: str) -> List[float]:
        """异步查询嵌入方法（BaseEmbedding要求）"""
        return self._get_simple_embedding(query)

    async def _aget_text_embedding(self, text: str) -> List[float]:
        """异步文本嵌入方法（BaseEmbedding要求）"""
        return self._get_simple_embedding(text)


def create_simple_embedding_model(embed_dim: int = 384) -> LlamaIndexSimpleEmbedding:
    """
    创建简单嵌入模型实例
    
    Args:
        embed_dim: 嵌入向量维度
        
    Returns:
        LlamaIndex兼容的嵌入模型
    """
    logger.info("创建简单嵌入模型")
    return LlamaIndexSimpleEmbedding(embed_dim)


# 测试函数
def test_simple_embedding():
    """测试简单嵌入模型"""
    print("🧪 测试简单嵌入模型...")
    
    model = create_simple_embedding_model()
    
    # 测试文本
    texts = [
        "这是一个测试文档",
        "知识库系统",
        "文档搜索功能",
        "这是一个测试文档"  # 重复文本，应该产生相同的嵌入
    ]
    
    # 获取嵌入向量
    embeddings = model.get_text_embeddings(texts)
    
    print(f"✅ 生成了 {len(embeddings)} 个嵌入向量")
    print(f"✅ 嵌入向量维度: {len(embeddings[0])}")
    
    # 测试相似度（使用简单的余弦相似度计算）
    def cosine_similarity(a, b):
        dot_product = sum(x * y for x, y in zip(a, b))
        norm_a = sum(x * x for x in a) ** 0.5
        norm_b = sum(y * y for y in b) ** 0.5
        if norm_a == 0 or norm_b == 0:
            return 0.0
        return (dot_product / (norm_a * norm_b) + 1) / 2

    similarity = cosine_similarity(embeddings[0], embeddings[3])
    print(f"✅ 相同文本相似度: {similarity:.3f}")

    similarity = cosine_similarity(embeddings[0], embeddings[1])
    print(f"✅ 不同文本相似度: {similarity:.3f}")
    
    print("🎉 简单嵌入模型测试完成!")


if __name__ == "__main__":
    test_simple_embedding()
