#!/usr/bin/env python3
"""
测试PDF上传和解析改进
"""
import requests
import json
import os

BASE_URL = "http://localhost:8000/api/v1"

def test_pdf_upload():
    """测试上传一个新的PDF文档"""
    print("🔍 测试PDF文档上传...")
    
    # 创建一个测试PDF内容
    test_content = """这是一个测试PDF文档。

包含中文内容：
- 知识库系统测试
- PDF解析功能验证
- 文本提取质量检查

English content:
- Knowledge base system test
- PDF parsing functionality verification
- Text extraction quality check

数字和符号：
123456789
!@#$%^&*()
"""
    
    try:
        # 模拟PDF文件上传
        files = {
            'file': ('test_pdf_parsing.txt', test_content.encode('utf-8'), 'text/plain')
        }
        
        response = requests.post(f"{BASE_URL}/upload", files=files, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 文档上传成功: {data.get('filename')}")
            print(f"  文档ID: {data.get('document_id')}")
            return data.get('document_id')
        else:
            print(f"❌ 文档上传失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return None

def test_document_status(doc_id):
    """测试文档处理状态"""
    print(f"\n🔍 检查文档状态: {doc_id}")
    
    try:
        response = requests.get(f"{BASE_URL}/documents", timeout=10)
        if response.status_code == 200:
            data = response.json()
            documents = data.get('documents', [])
            
            # 查找我们的文档
            for doc in documents:
                if doc.get('id') == doc_id:
                    print(f"📄 文档: {doc.get('filename')}")
                    print(f"   状态: {doc.get('status')}")
                    print(f"   文件大小: {doc.get('file_size', 0)} 字节")
                    print(f"   块数量: {doc.get('chunk_count', 0)}")
                    
                    # 显示内容预览
                    preview = doc.get('content_preview', '')
                    if preview:
                        print(f"   内容预览: {preview}")
                        
                        # 检查文本质量
                        if len(preview) > 10:
                            print("   ✅ 内容提取成功")
                        else:
                            print("   ⚠️ 内容过短")
                    else:
                        print("   ❌ 无内容预览")
                    
                    # 如果有错误信息，显示出来
                    if doc.get('error_message'):
                        print(f"   ❌ 错误: {doc.get('error_message')}")
                    
                    return doc
            
            print(f"❌ 未找到文档 {doc_id}")
            return None
            
    except Exception as e:
        print(f"❌ 检查状态失败: {e}")
        return None

def test_search_functionality(doc_id):
    """测试搜索功能"""
    print(f"\n🔍 测试搜索功能...")
    
    # 测试搜索查询
    search_queries = [
        "测试",
        "知识库",
        "PDF",
        "中文",
        "test"
    ]
    
    for query in search_queries:
        try:
            print(f"\n🔎 搜索: '{query}'")
            
            response = requests.post(f"{BASE_URL}/search", 
                json={"query": query, "top_k": 3}, 
                timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                print(f"   📊 找到 {len(results)} 个结果")
                
                for i, result in enumerate(results):
                    content = result.get('content', '')
                    score = result.get('score', 0)
                    doc_name = result.get('document_name', '')
                    
                    print(f"   结果 {i+1}: {doc_name} (相似度: {score:.3f})")
                    print(f"   内容: {content[:100]}...")
                    
                    # 检查是否是我们的文档
                    if result.get('document_id') == doc_id:
                        print("   ✅ 找到我们上传的文档")
            else:
                print(f"   ❌ 搜索失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 搜索异常: {e}")

def main():
    """主测试函数"""
    print("🚀 开始PDF解析改进测试...")
    print("=" * 60)
    
    # 1. 上传测试文档
    doc_id = test_pdf_upload()
    
    if doc_id:
        # 等待处理完成
        import time
        print("\n⏳ 等待文档处理...")
        time.sleep(3)
        
        # 2. 检查文档状态
        doc_info = test_document_status(doc_id)
        
        if doc_info and doc_info.get('status') == 'indexed':
            # 3. 测试搜索功能
            test_search_functionality(doc_id)
        else:
            print("⚠️ 文档未成功索引，跳过搜索测试")
    
    print("=" * 60)
    print("🎉 PDF解析改进测试完成!")

if __name__ == "__main__":
    main()
