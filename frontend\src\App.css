/* 知识库系统样式 */
#root {
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', '<PERSON>xy<PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 布局样式 */
.ant-layout {
  background: #f0f2f5;
}

.ant-layout-header {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

/* 卡片样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;
}

.ant-card-head {
  border-bottom: 1px solid #e8e8e8;
}

/* 上传区域样式 */
.ant-upload-drag {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.ant-upload-drag:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.ant-upload-drag.ant-upload-drag-hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

/* 搜索结果样式 */
.search-result-item {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.search-result-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 高亮文本样式 */
mark {
  background-color: #fff566;
  padding: 2px 4px;
  border-radius: 2px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  /* 大屏幕适配 */
  .ant-layout-content {
    padding: 0 16px;
  }
}

@media (max-width: 992px) {
  /* 中等屏幕适配 */
  .ant-layout-sider {
    width: 180px !important;
    min-width: 180px !important;
  }

  .ant-menu-item {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  /* 平板和移动端适配 */
  .ant-layout-content {
    margin-left: 0 !important;
    padding: 0 12px;
  }

  .ant-layout-header {
    padding: 0 16px;
  }

  /* 卡片间距调整 */
  .ant-card {
    margin-bottom: 12px;
  }

  /* 统计卡片响应式 */
  .ant-col {
    margin-bottom: 12px;
  }
}

@media (max-width: 576px) {
  /* 手机适配 */
  .ant-layout-header {
    padding: 0 12px;
    height: auto;
    min-height: 64px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .ant-layout-header > div:first-child {
    font-size: 16px;
    margin-bottom: 4px;
  }

  .ant-layout-header > div:last-child {
    font-size: 12px;
  }

  .ant-layout-content {
    padding: 0 8px;
  }

  /* 表格响应式 */
  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-table {
    min-width: 600px;
  }

  /* 按钮组响应式 */
  .ant-space {
    flex-wrap: wrap;
  }

  /* 模态框响应式 */
  .ant-modal {
    margin: 8px;
    max-width: calc(100vw - 16px);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载动画 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  flex-direction: column;
}

/* 统计卡片样式 */
.ant-statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 搜索界面优化 */
.search-input-large .ant-input {
  font-size: 16px;
  min-height: 50px;
}

.search-input-large .ant-input-search-button {
  height: 50px;
  font-size: 16px;
}

.search-input-large .ant-btn {
  height: 50px;
  font-size: 16px;
}

/* 搜索提示样式 */
.search-tips {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
}

/* 响应式搜索框 */
@media (max-width: 768px) {
  .search-input-large .ant-input {
    font-size: 14px !important;
    min-height: 44px !important;
  }

  .search-input-large .ant-input-search-button,
  .search-input-large .ant-btn {
    height: 44px !important;
    font-size: 14px !important;
  }

  /* 搜索提示在移动端的样式 */
  .search-tips {
    padding: 12px !important;
    font-size: 12px !important;
  }
}

/* 移动端优化 */
@media (max-width: 576px) {
  /* 卡片标题字体大小 */
  .ant-card-head-title {
    font-size: 14px !important;
  }

  /* 按钮组间距 */
  .ant-space-item {
    margin-bottom: 4px !important;
  }

  /* 标签字体大小 */
  .ant-tag {
    font-size: 11px !important;
    padding: 0 4px !important;
  }

  /* 进度条高度 */
  .ant-progress-line {
    font-size: 12px !important;
  }

  /* 统计数字大小 */
  .ant-statistic-content-value {
    font-size: 20px !important;
  }

  /* 列表项间距 */
  .ant-list-item {
    padding: 8px 0 !important;
  }

  /* 描述列表响应式 */
  .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    font-size: 12px !important;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .ant-layout-header {
    padding: 0 8px !important;
  }

  .ant-layout-content {
    padding: 0 4px !important;
  }

  /* 卡片内边距 */
  .ant-card-body {
    padding: 12px !important;
  }

  /* 表格字体 */
  .ant-table {
    font-size: 12px !important;
  }

  .ant-table-thead > tr > th {
    padding: 8px 4px !important;
    font-size: 11px !important;
  }

  .ant-table-tbody > tr > td {
    padding: 8px 4px !important;
    font-size: 11px !important;
  }
}
