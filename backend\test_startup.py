"""
测试后端启动脚本
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有必要的导入"""
    print("测试导入...")
    
    try:
        # 测试基本导入
        import fastapi
        print("✅ FastAPI 导入成功")
        
        import uvicorn
        print("✅ Uvicorn 导入成功")
        
        # 测试LlamaIndex导入
        from llama_index.core import VectorStoreIndex, Document, Settings
        print("✅ LlamaIndex Core 导入成功")
        
        from llama_index.embeddings.openai import OpenAIEmbedding
        print("✅ OpenAI Embeddings 导入成功")
        
        from llama_index.llms.openai import OpenAI
        print("✅ OpenAI LLM 导入成功")
        
        # 测试文档处理库
        import PyPDF2
        print("✅ PyPDF2 导入成功")
        
        import docx
        print("✅ python-docx 导入成功")
        
        import markdown
        print("✅ Markdown 导入成功")
        
        # 测试应用导入
        from app.core.config import get_settings
        print("✅ 应用配置 导入成功")
        
        from app.services.knowledge_base_service import KnowledgeBaseService
        print("✅ 知识库服务 导入成功")
        
        print("\n🎉 所有导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n测试配置...")
    
    try:
        from app.core.config import get_settings, validate_config
        
        settings = get_settings()
        print(f"✅ 应用名称: {settings.app_name}")
        print(f"✅ 版本: {settings.app_version}")
        print(f"✅ 上传路径: {settings.upload_path}")
        print(f"✅ 向量存储路径: {settings.vector_store_path}")
        
        # 检查OpenAI API Key
        if settings.openai_api_key == "your_openai_api_key_here":
            print("⚠️  警告: 请设置真实的 OPENAI_API_KEY")
        else:
            print("✅ OpenAI API Key 已设置")
        
        # 验证配置
        try:
            validate_config()
            print("✅ 配置验证通过")
        except ValueError as e:
            print(f"⚠️  配置警告: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_services():
    """测试服务初始化"""
    print("\n测试服务初始化...")
    
    try:
        # 临时设置一个假的API key来测试
        os.environ['OPENAI_API_KEY'] = 'test-key'
        
        from app.services.document_processor import DocumentProcessor
        processor = DocumentProcessor()
        print("✅ 文档处理器 初始化成功")
        
        # 注意：LlamaIndexService需要真实的API key，这里只测试导入
        from app.services.llama_service import LlamaIndexService
        print("✅ LlamaIndex服务 导入成功")
        
        from app.services.knowledge_base_service import KnowledgeBaseService
        print("✅ 知识库服务 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试后端组件...")
    
    success = True
    success &= test_imports()
    success &= test_config()
    success &= test_services()
    
    if success:
        print("\n🎉 所有测试通过！后端可以启动。")
        print("\n下一步:")
        print("1. 设置真实的 OPENAI_API_KEY 在 .env 文件中")
        print("2. 运行: python -m uvicorn app.main:app --reload")
    else:
        print("\n❌ 测试失败，请检查依赖安装。")
        print("运行: pip install -r requirements.txt")
    
    input("\n按回车键退出...")
