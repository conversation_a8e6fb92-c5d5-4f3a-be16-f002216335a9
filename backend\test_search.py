#!/usr/bin/env python3
"""
测试搜索功能
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_search_with_timeout():
    """测试搜索功能（带超时）"""
    print("🔍 测试搜索功能...")
    
    try:
        data = {"query": "知识库", "top_k": 3}
        response = requests.post(f"{BASE_URL}/search", json=data, timeout=10)
        
        print(f"搜索状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 搜索成功")
            print(f"搜索时间: {result.get('search_time', 0):.3f}秒")
            print(f"结果数量: {result.get('total_results', 0)}")
            
            for i, res in enumerate(result.get('results', []), 1):
                print(f"\n结果 {i}:")
                print(f"  文档: {res.get('document_name', 'Unknown')}")
                print(f"  相关度: {res.get('score', 0):.3f}")
                print(f"  内容: {res.get('content', '')[:100]}...")
        else:
            print(f"❌ 搜索失败: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 搜索超时（>10秒）")
    except Exception as e:
        print(f"❌ 搜索异常: {e}")

def main():
    """主函数"""
    print("🚀 开始搜索测试...")
    test_search_with_timeout()
    print("\n🎉 搜索测试完成!")

if __name__ == "__main__":
    main()
