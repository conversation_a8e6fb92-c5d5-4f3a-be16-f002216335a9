/**
 * 测试搜索组件 - 用于调试搜索输入问题
 */
import React, { useState } from 'react';
import { Input, Button, Card, Space } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

const { Search } = Input;

const TestSearch = () => {
  const [query, setQuery] = useState('');
  const [testValue, setTestValue] = useState('');

  const handleSearch = (value) => {
    console.log('搜索内容:', value);
    alert(`搜索内容: ${value}`);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card title="🔧 搜索功能测试" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          
          {/* 基础输入框测试 */}
          <div>
            <h4>基础输入框测试:</h4>
            <Input
              placeholder="请输入测试内容..."
              value={testValue}
              onChange={(e) => setTestValue(e.target.value)}
              style={{ marginBottom: '8px' }}
            />
            <p>当前值: {testValue}</p>
          </div>

          {/* 搜索框测试1 */}
          <div>
            <h4>搜索框测试1 (简单版):</h4>
            <Search
              placeholder="输入搜索内容..."
              allowClear
              enterButton="搜索"
              size="large"
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </div>

          {/* 搜索框测试2 */}
          <div>
            <h4>搜索框测试2 (受控组件):</h4>
            <Search
              placeholder="输入搜索内容..."
              allowClear
              enterButton={
                <Button type="primary" icon={<SearchOutlined />}>
                  搜索
                </Button>
              }
              size="large"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
            <p>当前搜索值: {query}</p>
          </div>

          {/* 测试按钮 */}
          <div>
            <Space>
              <Button onClick={() => setQuery('测试内容')}>
                设置测试内容
              </Button>
              <Button onClick={() => setQuery('')}>
                清空内容
              </Button>
              <Button type="primary" onClick={() => handleSearch(query)}>
                执行搜索
              </Button>
            </Space>
          </div>

        </Space>
      </Card>
    </div>
  );
};

export default TestSearch;
