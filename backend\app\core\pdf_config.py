"""
PDF解析配置
"""
from typing import Dict, Any, List
from dataclasses import dataclass

@dataclass
class PDFExtractionConfig:
    """PDF提取配置"""
    
    # 质量阈值
    MIN_TEXT_LENGTH: int = 30
    HIGH_QUALITY_THRESHOLD: float = 0.75
    ACCEPTABLE_QUALITY_THRESHOLD: float = 0.4
    MAX_SPECIAL_CHAR_RATIO: float = 0.6
    
    # 文本密度阈值
    HIGH_TEXT_DENSITY_THRESHOLD: float = 0.8
    LOW_TEXT_DENSITY_THRESHOLD: float = 0.3
    
    # pdfplumber参数
    PDFPLUMBER_TOLERANCE_PRECISE: Dict[str, int] = None
    PDFPLUMBER_TOLERANCE_NORMAL: Dict[str, int] = None
    PDFPLUMBER_TOLERANCE_LOOSE: Dict[str, int] = None
    
    # 文本清理参数
    MIN_LINE_LENGTH: int = 2
    MIN_MEANINGFUL_CHARS: int = 2
    MAX_SPECIAL_CHAR_RATIO_PER_LINE: float = 0.7
    
    # 表格检测参数
    MIN_TABLE_CELLS: int = 4
    TABLE_HEADER_INDICATORS: List[str] = None
    
    # 编码修复参数
    ENCODING_REPLACEMENT_CHARS: List[str] = None
    
    def __post_init__(self):
        """初始化默认值"""
        if self.PDFPLUMBER_TOLERANCE_PRECISE is None:
            self.PDFPLUMBER_TOLERANCE_PRECISE = {'x_tolerance': 1, 'y_tolerance': 1}
        
        if self.PDFPLUMBER_TOLERANCE_NORMAL is None:
            self.PDFPLUMBER_TOLERANCE_NORMAL = {'x_tolerance': 3, 'y_tolerance': 3}
        
        if self.PDFPLUMBER_TOLERANCE_LOOSE is None:
            self.PDFPLUMBER_TOLERANCE_LOOSE = {'x_tolerance': 5, 'y_tolerance': 5}
        
        if self.TABLE_HEADER_INDICATORS is None:
            self.TABLE_HEADER_INDICATORS = [
                '名称', '类型', '数量', '价格', '日期', '编号', '序号',
                'name', 'type', 'count', 'price', 'date', 'id', 'no',
                '项目', '内容', '说明', '备注', '状态', '结果'
            ]
        
        if self.ENCODING_REPLACEMENT_CHARS is None:
            self.ENCODING_REPLACEMENT_CHARS = ['\ufffd', '\x00']

class PDFMethodSelector:
    """PDF解析方法选择器"""
    
    @staticmethod
    def get_method_priority(pdf_info: Dict[str, Any]) -> List[str]:
        """根据PDF特征返回方法优先级列表"""
        
        is_scanned = pdf_info.get('is_scanned', False)
        has_tables = pdf_info.get('has_tables', False)
        has_encoding_issues = pdf_info.get('encoding_issues', False)
        text_density = pdf_info.get('text_density', 0.0)
        page_count = pdf_info.get('page_count', 0)
        
        if is_scanned:
            # 扫描版PDF优先级
            return [
                'pdfplumber图像优化模式',
                'pdfplumber高级模式',
                'PyPDF2兼容模式'
            ]
        elif has_tables:
            # 表格PDF优先级
            return [
                'pdfplumber表格优化模式',
                'pdfplumber高级模式',
                'pdfplumber基础模式'
            ]
        elif has_encoding_issues:
            # 编码问题PDF优先级
            return [
                'pdfplumber编码优化模式',
                'PyPDF2编码修复模式',
                'pdfplumber基础模式'
            ]
        elif text_density > 0.8:
            # 高文本密度PDF优先级
            return [
                'pdfplumber高级模式',
                'pdfplumber基础模式',
                'PyPDF2标准模式'
            ]
        elif page_count > 50:
            # 大文档优先级（性能优先）
            return [
                'pdfplumber基础模式',
                'PyPDF2标准模式',
                'pdfplumber高级模式'
            ]
        else:
            # 标准PDF优先级
            return [
                'pdfplumber高级模式',
                'pdfplumber基础模式',
                'PyPDF2标准模式'
            ]

class PDFQualityAssessor:
    """PDF文本质量评估器"""
    
    @staticmethod
    def assess_content_type(text: str) -> Dict[str, Any]:
        """评估内容类型"""
        import re
        
        assessment = {
            'content_type': 'unknown',
            'language': 'unknown',
            'has_tables': False,
            'has_formulas': False,
            'has_references': False,
            'structure_score': 0.0
        }
        
        if not text:
            return assessment
        
        # 语言检测
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        if chinese_chars > english_chars:
            assessment['language'] = 'chinese'
        elif english_chars > chinese_chars:
            assessment['language'] = 'english'
        else:
            assessment['language'] = 'mixed'
        
        # 内容类型检测
        if re.search(r'摘要|abstract|关键词|keywords', text, re.IGNORECASE):
            assessment['content_type'] = 'academic'
        elif re.search(r'合同|协议|contract|agreement', text, re.IGNORECASE):
            assessment['content_type'] = 'legal'
        elif re.search(r'报告|report|分析|analysis', text, re.IGNORECASE):
            assessment['content_type'] = 'report'
        elif '|' in text and text.count('|') > 10:
            assessment['content_type'] = 'table_heavy'
        
        # 表格检测
        if '|' in text and text.count('|') > 5:
            assessment['has_tables'] = True
        
        # 公式检测
        if re.search(r'[=+\-*/()]\s*\d+|∑|∫|√', text):
            assessment['has_formulas'] = True
        
        # 参考文献检测
        if re.search(r'参考文献|references|bibliography|\[\d+\]', text, re.IGNORECASE):
            assessment['has_references'] = True
        
        # 结构评分
        lines = text.split('\n')
        non_empty_lines = [line.strip() for line in lines if line.strip()]
        
        if len(non_empty_lines) > 0:
            avg_line_length = sum(len(line) for line in non_empty_lines) / len(non_empty_lines)
            
            # 基于行长度分布评估结构
            if 20 <= avg_line_length <= 80:
                assessment['structure_score'] = 0.8
            elif 10 <= avg_line_length <= 120:
                assessment['structure_score'] = 0.6
            else:
                assessment['structure_score'] = 0.4
        
        return assessment

class PDFOptimizationStrategy:
    """PDF优化策略"""
    
    @staticmethod
    def get_optimization_params(pdf_info: Dict[str, Any], content_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """获取优化参数"""
        
        params = {
            'use_layout_analysis': False,
            'aggressive_cleaning': False,
            'preserve_formatting': False,
            'extract_tables_separately': False,
            'apply_encoding_fixes': False,
            'use_ocr_fallback': False
        }
        
        # 根据PDF特征调整参数
        if pdf_info.get('is_scanned', False):
            params['use_ocr_fallback'] = True
            params['aggressive_cleaning'] = True
        
        if pdf_info.get('has_tables', False) or content_assessment.get('has_tables', False):
            params['extract_tables_separately'] = True
            params['preserve_formatting'] = True
        
        if pdf_info.get('encoding_issues', False):
            params['apply_encoding_fixes'] = True
            params['aggressive_cleaning'] = True
        
        if content_assessment.get('content_type') == 'academic':
            params['preserve_formatting'] = True
            params['use_layout_analysis'] = True
        
        if pdf_info.get('text_density', 0) < 0.3:
            params['use_layout_analysis'] = True
        
        return params

# 默认配置实例
DEFAULT_PDF_CONFIG = PDFExtractionConfig()

# 预定义的配置模板
PDF_CONFIG_TEMPLATES = {
    'fast': PDFExtractionConfig(
        HIGH_QUALITY_THRESHOLD=0.6,
        ACCEPTABLE_QUALITY_THRESHOLD=0.3,
        MIN_TEXT_LENGTH=20
    ),
    'accurate': PDFExtractionConfig(
        HIGH_QUALITY_THRESHOLD=0.8,
        ACCEPTABLE_QUALITY_THRESHOLD=0.5,
        MIN_TEXT_LENGTH=50
    ),
    'balanced': PDFExtractionConfig()  # 使用默认值
}
