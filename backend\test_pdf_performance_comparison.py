#!/usr/bin/env python3
"""
PDF解析性能对比测试
"""
import asyncio
import os
import sys
import time
from pathlib import Path
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from app.services.document_processor import DocumentProcessor
from app.models.schemas import FileType
from app.core.config import get_settings

class PDFPerformanceTester:
    """PDF解析性能测试器"""
    
    def __init__(self):
        self.processor = DocumentProcessor()
        self.results = []
    
    async def run_performance_test(self, pdf_file: Path) -> Dict:
        """运行性能测试"""
        print(f"\n📄 测试文件: {pdf_file.name}")
        print(f"📏 文件大小: {pdf_file.stat().st_size / 1024:.1f} KB")
        
        # 分析PDF结构
        pdf_info = self.processor._analyze_pdf_structure(str(pdf_file))
        print(f"📊 PDF信息: {pdf_info}")
        
        # 获取最优解析方法
        methods = self.processor._get_optimal_extraction_methods(pdf_info)
        
        test_results = {
            'filename': pdf_file.name,
            'file_size_kb': pdf_file.stat().st_size / 1024,
            'pdf_info': pdf_info,
            'method_results': []
        }
        
        # 测试每种解析方法
        for method_info in methods:
            method_name = method_info['name']
            method_func = method_info['func']
            
            print(f"\n🔧 测试方法: {method_name}")
            
            try:
                # 记录开始时间
                start_time = time.time()
                
                # 执行解析
                result = await method_func(str(pdf_file), pdf_info)
                
                # 记录结束时间
                end_time = time.time()
                processing_time = end_time - start_time
                
                # 评估质量
                quality_score = self.processor._assess_text_quality_enhanced(result) if result else 0.0
                
                method_result = {
                    'method': method_name,
                    'success': True,
                    'processing_time': processing_time,
                    'text_length': len(result) if result else 0,
                    'quality_score': quality_score,
                    'words_count': len(result.split()) if result else 0,
                    'lines_count': len(result.split('\n')) if result else 0
                }
                
                print(f"   ✅ 成功 - 时间: {processing_time:.2f}s, 质量: {quality_score:.3f}, 长度: {len(result) if result else 0}")
                
            except Exception as e:
                method_result = {
                    'method': method_name,
                    'success': False,
                    'processing_time': 0,
                    'text_length': 0,
                    'quality_score': 0.0,
                    'error': str(e)
                }
                
                print(f"   ❌ 失败 - {e}")
            
            test_results['method_results'].append(method_result)
        
        return test_results
    
    def analyze_results(self, all_results: List[Dict]):
        """分析测试结果"""
        print(f"\n{'='*80}")
        print("📊 性能分析报告")
        print(f"{'='*80}")
        
        # 按方法统计
        method_stats = {}
        
        for file_result in all_results:
            for method_result in file_result['method_results']:
                method_name = method_result['method']
                
                if method_name not in method_stats:
                    method_stats[method_name] = {
                        'total_tests': 0,
                        'successful_tests': 0,
                        'total_time': 0,
                        'total_quality': 0,
                        'total_length': 0
                    }
                
                stats = method_stats[method_name]
                stats['total_tests'] += 1
                
                if method_result['success']:
                    stats['successful_tests'] += 1
                    stats['total_time'] += method_result['processing_time']
                    stats['total_quality'] += method_result['quality_score']
                    stats['total_length'] += method_result['text_length']
        
        # 输出统计结果
        print("\n🏆 方法性能排名:")
        print("-" * 80)
        
        # 按成功率和质量排序
        sorted_methods = sorted(
            method_stats.items(),
            key=lambda x: (
                x[1]['successful_tests'] / x[1]['total_tests'],  # 成功率
                x[1]['total_quality'] / max(x[1]['successful_tests'], 1)  # 平均质量
            ),
            reverse=True
        )
        
        for rank, (method_name, stats) in enumerate(sorted_methods, 1):
            success_rate = stats['successful_tests'] / stats['total_tests'] * 100
            avg_time = stats['total_time'] / max(stats['successful_tests'], 1)
            avg_quality = stats['total_quality'] / max(stats['successful_tests'], 1)
            avg_length = stats['total_length'] / max(stats['successful_tests'], 1)
            
            print(f"{rank}. {method_name}")
            print(f"   成功率: {success_rate:.1f}% ({stats['successful_tests']}/{stats['total_tests']})")
            print(f"   平均时间: {avg_time:.2f}秒")
            print(f"   平均质量: {avg_quality:.3f}")
            print(f"   平均长度: {avg_length:.0f}字符")
            print()
        
        # 文件类型分析
        print("\n📋 文件类型分析:")
        print("-" * 80)
        
        for file_result in all_results:
            pdf_info = file_result['pdf_info']
            best_method = None
            best_score = 0
            
            for method_result in file_result['method_results']:
                if method_result['success'] and method_result['quality_score'] > best_score:
                    best_method = method_result
                    best_score = method_result['quality_score']
            
            print(f"📄 {file_result['filename']}")
            print(f"   文件大小: {file_result['file_size_kb']:.1f} KB")
            print(f"   页数: {pdf_info.get('page_count', 0)}")
            print(f"   文本密度: {pdf_info.get('text_density', 0):.2f}")
            print(f"   是否扫描版: {'是' if pdf_info.get('is_scanned') else '否'}")
            print(f"   包含表格: {'是' if pdf_info.get('has_tables') else '否'}")
            
            if best_method:
                print(f"   最佳方法: {best_method['method']}")
                print(f"   最佳质量: {best_method['quality_score']:.3f}")
                print(f"   处理时间: {best_method['processing_time']:.2f}秒")
            else:
                print(f"   ❌ 所有方法都失败")
            print()

async def main():
    """主函数"""
    print("🚀 PDF解析性能对比测试")
    print("=" * 80)
    
    tester = PDFPerformanceTester()
    
    # 查找测试文件
    upload_dir = Path(tester.processor.settings.upload_path)
    pdf_files = list(upload_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 未找到PDF文件进行测试")
        print(f"请将PDF文件放置在: {upload_dir}")
        return
    
    print(f"📁 找到 {len(pdf_files)} 个PDF文件")
    
    # 限制测试文件数量
    test_files = pdf_files[:5]  # 最多测试5个文件
    
    all_results = []
    
    # 测试每个文件
    for pdf_file in test_files:
        try:
            result = await tester.run_performance_test(pdf_file)
            all_results.append(result)
        except Exception as e:
            print(f"❌ 测试文件 {pdf_file.name} 失败: {e}")
    
    # 分析结果
    if all_results:
        tester.analyze_results(all_results)
    else:
        print("❌ 没有成功的测试结果")

if __name__ == "__main__":
    asyncio.run(main())
